{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Valisights\\\\valisight-app\\\\src\\\\pages\\\\reports\\\\ReportPages\\\\FiscalYear.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from \"react\";\nimport ApexCharts from \"apexcharts\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FiscalYearDashboard = ({\n  headerTextStyle = {},\n  headingTextStyle = {},\n  subHeadingTextStyle = {},\n  contentTextStyle = {},\n  fiscalData = null,\n  contentSettings = null // Add contentSettings prop\n}) => {\n  _s();\n  const stackedColumnRef = useRef(null);\n  const netIncomeRef = useRef(null);\n  const grossProfitRef = useRef(null);\n  const netProfitMarginRef = useRef(null);\n\n  // Enhanced data validation function - more lenient approach\n  const isDataLoaded = () => {\n    if (!fiscalData) {\n      return false;\n    }\n\n    // Check if at least one of the required data arrays exists and has content\n    const hasMonthlyData = fiscalData.monthlyPerformanceBreakDown && Array.isArray(fiscalData.monthlyPerformanceBreakDown) && fiscalData.monthlyPerformanceBreakDown.length > 0;\n    const hasGrossProfitMargin = fiscalData.monthlyGrossProfitMargin && Array.isArray(fiscalData.monthlyGrossProfitMargin) && fiscalData.monthlyGrossProfitMargin.length > 0;\n    const hasNetProfitMargin = fiscalData.nerProfitMargin && Array.isArray(fiscalData.nerProfitMargin) && fiscalData.nerProfitMargin.length > 0;\n    const hasNetIncomeData = fiscalData.netIncomeLoss && Array.isArray(fiscalData.netIncomeLoss) && fiscalData.netIncomeLoss.length > 0;\n    // We'll handle missing other data gracefully in the charts\n    return hasMonthlyData;\n  };\n\n  // Function to check if we have any meaningful data (not all zeros)\n  const hasAnyUsableData = () => {\n    if (!(fiscalData !== null && fiscalData !== void 0 && fiscalData.monthlyPerformanceBreakDown)) {\n      return false;\n    }\n    const monthlyData = fiscalData.monthlyPerformanceBreakDown;\n\n    // Check if any monthly data has meaningful values\n    const hasMeaningfulMonthlyData = monthlyData.some(item => {\n      const income = parseFloat(item.totalIncome || 0);\n      const cogs = parseFloat(item.totalCOGS || 0);\n      const expenses = parseFloat(item.totalExpenses || 0);\n      return income > 0 || cogs > 0 || expenses > 0;\n    });\n    return hasMeaningfulMonthlyData;\n  };\n\n  // Function to check if a chart should be displayed based on content settings\n  const shouldDisplayChart = chartKey => {\n    if (!(contentSettings !== null && contentSettings !== void 0 && contentSettings.chartSettings)) return true; // Default to true if no settings\n    return contentSettings.chartSettings[chartKey] === true;\n  };\n  useEffect(() => {\n    if (isDataLoaded()) {\n      // Clear charts first\n      [stackedColumnRef, netIncomeRef, grossProfitRef, netProfitMarginRef].forEach(ref => {\n        if (ref.current) {\n          ref.current.innerHTML = \"\";\n        }\n      });\n      // Initialize charts with new data\n      initializeCharts();\n    }\n  }, [fiscalData, contentSettings]); // Add contentSettings to dependency array\n\n  const formatMonthYear = (year, month) => {\n    const monthNames = [\"Jan\", \"Feb\", \"Mar\", \"Apr\", \"May\", \"Jun\", \"Jul\", \"Aug\", \"Sep\", \"Oct\", \"Nov\", \"Dec\"];\n    return `${monthNames[month - 1]} ${String(year).slice(-2)}`;\n  };\n  function formatNumber(num) {\n    // Round to 2 decimal places to fix floating point precision issues\n    const roundedNum = Math.round(num * 100) / 100;\n    const isNegative = roundedNum < 0;\n    const absNum = Math.abs(roundedNum);\n\n    // For numbers under 10k, show with appropriate decimal places (no suffix)\n    if (absNum < 1000) {\n      return (isNegative ? \"-\" : \"\") + (absNum % 1 === 0 ? absNum.toString() : absNum.toFixed(2));\n    }\n    const suffixes = [{\n      value: 1e12,\n      suffix: \"T\"\n    }, {\n      value: 1e9,\n      suffix: \"B\"\n    }, {\n      value: 1e6,\n      suffix: \"M\"\n    }, {\n      value: 1e3,\n      suffix: \"K\"\n    }];\n    for (let i = 0; i < suffixes.length; i++) {\n      if (absNum >= suffixes[i].value) {\n        const formatted = (absNum / suffixes[i].value).toFixed(1);\n        const cleanFormatted = formatted.endsWith(\".0\") ? formatted.slice(0, -2) : formatted;\n        return (isNegative ? \"-\" : \"\") + cleanFormatted + suffixes[i].suffix;\n      }\n    }\n    return (isNegative ? \"-\" : \"\") + roundedNum.toString();\n  }\n  const initializeCharts = () => {\n    if (!(fiscalData !== null && fiscalData !== void 0 && fiscalData.monthlyPerformanceBreakDown)) return;\n    const monthlyData = fiscalData.monthlyPerformanceBreakDown;\n\n    // Create categories from monthlyPerformanceBreakDown\n    const categories = monthlyData.map(item => formatMonthYear(item.year, item.month));\n\n    // Create lookup maps with fallback for missing data\n    const netProfitMarginMap = new Map();\n    if (fiscalData.nerProfitMargin && Array.isArray(fiscalData.nerProfitMargin)) {\n      fiscalData.nerProfitMargin.forEach(item => {\n        const key = `${item.year}-${item.month}`;\n        const value = parseFloat(item.nerProfitMargin) || 0;\n        netProfitMarginMap.set(key, value);\n      });\n    }\n    const grossProfitMarginMap = new Map();\n    if (fiscalData.monthlyGrossProfitMargin && Array.isArray(fiscalData.monthlyGrossProfitMargin)) {\n      fiscalData.monthlyGrossProfitMargin.forEach(item => {\n        const key = `${item.year}-${item.month}`;\n        const value = parseFloat(item.Gross_Profit_Margin);\n        const percentage = value > 1 ? value : value * 100;\n        grossProfitMarginMap.set(key, percentage || 0);\n      });\n    }\n    const netIncomeMap = new Map();\n    if (fiscalData.netIncomeLoss && Array.isArray(fiscalData.netIncomeLoss)) {\n      fiscalData.netIncomeLoss.forEach(item => {\n        const key = `${item.year}-${item.month}`;\n        const value = parseFloat(item.netIncomeLoss) / 1000 || 0;\n        netIncomeMap.set(key, value);\n      });\n    }\n\n    // Create aligned data arrays\n    const incomeData = monthlyData.map(item => parseFloat(item.totalIncome) / 1000 || 0);\n    const cogsData = monthlyData.map(item => parseFloat(item.totalCOGS) / 1000 || 0);\n    const expenseData = monthlyData.map(item => parseFloat(item.totalExpenses) / 1000 || 0);\n    const grossProfitMarginData = monthlyData.map(item => {\n      const key = `${item.year}-${item.month}`;\n      return grossProfitMarginMap.get(key) || 0;\n    });\n    console.log(\"gross profit margin\", grossProfitMarginData);\n    const netProfitMarginRaw = monthlyData.map(item => {\n      const key = `${item.year}-${item.month}`;\n      return netProfitMarginMap.get(key) || 0;\n    });\n    const netIncomeData = monthlyData.map(item => {\n      const key = `${item.year}-${item.month}`;\n      return netIncomeMap.get(key) || 0;\n    });\n\n    // 1. Stacked Column Chart (incomeSummary)\n    const stackedColumnOptions = {\n      series: [{\n        name: \"Income\",\n        type: \"line\",\n        data: incomeData\n      }, {\n        name: \"Expense\",\n        type: \"column\",\n        data: expenseData\n      }, {\n        name: \"Cost of Goods Sold\",\n        type: \"column\",\n        data: cogsData\n      }],\n      chart: {\n        height: 450,\n        type: \"bar\",\n        stacked: true,\n        toolbar: {\n          show: false\n        },\n        background: \"transparent\"\n      },\n      dataLabels: {\n        enabled: true,\n        enabledOnSeries: [0, 1, 2],\n        // Show labels on all three series: Income, Expense, and COGS\n        formatter: function (val, opts) {\n          if (val === null || val === undefined || isNaN(val)) return \"\";\n          const absVal = Math.abs(val);\n          if (absVal >= 1000) {\n            return '$' + (val / 1000).toFixed(1) + 'm';\n          } else if (absVal >= 1) {\n            return '$' + val.toFixed(2) + 'k';\n          } else {\n            return '$' + (val * 1000).toFixed(0);\n          }\n        },\n        style: {\n          fontSize: \"14px\",\n          colors: [\"#20b2aa\", \"#333\", \"#333\"],\n          // Different colors for each series\n          fontWeight: \"500\"\n        },\n        offsetY: -10,\n        background: {\n          enabled: false\n        },\n        dropShadow: {\n          enabled: false\n        }\n      },\n      stroke: {\n        width: [2, 0, 0],\n        curve: \"smooth\"\n      },\n      plotOptions: {\n        bar: {\n          columnWidth: \"60%\",\n          dataLabels: {\n            total: {\n              enabled: false,\n              // Disable total labels\n              offsetY: -20,\n              style: {\n                fontSize: \"14px\",\n                fontWeight: \"500\",\n                color: \"#333\"\n              },\n              formatter: function (val) {\n                if (val === null || val === undefined || isNaN(val)) return \"$0\";\n                const absVal = Math.abs(val);\n                if (absVal >= 1000) {\n                  return '$' + (val / 1000).toFixed(1) + 'm';\n                } else if (absVal >= 1) {\n                  return '$' + val.toFixed(1) + 'k';\n                } else {\n                  return '$' + (val * 1000).toFixed(0);\n                }\n              }\n            }\n          }\n        }\n      },\n      fill: {\n        opacity: [1, 1, 1] // Make all series fully opaque\n      },\n      labels: categories,\n      markers: {\n        size: [5, 0, 0],\n        fontSize: \"14px\",\n        strokeColors: \"#fff\",\n        strokeWidth: 2,\n        fillOpacity: 1,\n        hover: {\n          size: 7\n        }\n      },\n      xaxis: {\n        labels: {\n          style: {\n            colors: \"#666\",\n            fontSize: \"14px\"\n          },\n          offsetY: 15 // Push month labels down by 15px\n        },\n        axisBorder: {\n          show: false\n        },\n        axisTicks: {\n          show: false\n        }\n      },\n      yaxis: {\n        show: false,\n        // More conservative scaling to ensure small COGS values are visible\n        min: 0,\n        max: function () {\n          // Calculate max considering stacked values\n          const maxStacked = Math.max(...cogsData.map((cogs, i) => cogs + expenseData[i]));\n          const maxIncome = Math.max(...incomeData);\n          return Math.max(maxStacked, maxIncome) * 1.2;\n        }\n      },\n      colors: [\"#20b2aa\", \"#ff8a80\", \"#53579f\"],\n      // Colors for Income, Expense, COGS\n      legend: {\n        position: \"bottom\",\n        horizontalAlign: \"center\",\n        fontSize: \"14px\",\n        fontWeight: \"400\",\n        markers: {\n          width: 12,\n          height: 12,\n          radius: 6 // Circular markers\n        },\n        labels: {\n          colors: \"#333\",\n          useSeriesColors: false\n        },\n        itemMargin: {\n          horizontal: 15,\n          vertical: 4\n        },\n        offsetY: 10,\n        onItemClick: {\n          toggleDataSeries: true // Enable clicking to hide/show series\n        },\n        onItemHover: {\n          highlightDataSeries: true // Enable hover highlighting\n        }\n      },\n      tooltip: {\n        shared: true,\n        intersect: false,\n        // Custom tooltip to clearly show all values\n        custom: function ({\n          series,\n          seriesIndex,\n          dataPointIndex,\n          w\n        }) {\n          const income = (series[0][dataPointIndex] * 1000).toFixed(0);\n          const expense = (series[1][dataPointIndex] * 1000).toFixed(0);\n          const cogs = (series[2][dataPointIndex] * 1000).toFixed(0);\n          const category = w.globals.labels[dataPointIndex];\n          return `\n        <div style=\"padding: 12px; background: white; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); font-size: 14px;\">\n          <div style=\"font-weight: 600; margin-bottom: 8px; color: #333;\">${category}</div>\n          <div style=\"display: flex; align-items: center; margin-bottom: 4px;\">\n            <div style=\"width: 12px; height: 12px; background: #20b2aa; border-radius: 50%; margin-right: 8px;\"></div>\n            <span style=\"color: #333;\">Income: <strong>$${income}</strong></span>\n          </div>\n          <div style=\"display: flex; align-items: center; margin-bottom: 4px;\">\n            <div style=\"width: 12px; height: 12px; background: #ff8a80; border-radius: 50%; margin-right: 8px;\"></div>\n            <span style=\"color: #333;\">Expense: <strong>$${expense}</strong></span>\n          </div>\n          <div style=\"display: flex; align-items: center; margin-bottom: 4px; ${cogs == 0 ? 'opacity: 0.6;' : ''}\">\n            <div style=\"width: 12px; height: 12px; background: #4361ee; border-radius: 50%; margin-right: 8px;\"></div>\n            <span style=\"color: #333;\">COGS: <strong>$${cogs}</strong> ${cogs == 0 ? '(No data)' : ''}</span>\n          </div>\n          <div style=\"margin-top: 8px; padding-top: 8px; border-top: 1px solid #eee; font-size: 12px; color: #666;\">\n            Total Costs: <strong>$${(parseFloat(expense) + parseFloat(cogs)).toFixed(0)}</strong>\n          </div>\n        </div>\n      `;\n        }\n      },\n      grid: {\n        show: false,\n        padding: {\n          left: 25,\n          right: 25,\n          top: 20,\n          bottom: 30 // Increased bottom padding from 0 to 30\n        }\n      },\n      // Add annotations to highlight if COGS data exists\n      annotations: {\n        yaxis: cogsData.some(val => val > 0) ? [] : [{\n          y: 0,\n          borderColor: '#FF4560',\n          label: {\n            borderColor: '#FF4560',\n            style: {\n              color: '#fff',\n              background: '#FF4560'\n            }\n          }\n        }]\n      }\n    };\n\n    // 2. Net Income Chart\n    const netIncomeOptions = {\n      series: [{\n        name: 'Net Income',\n        data: netIncomeData\n      }],\n      chart: {\n        type: 'line',\n        height: 300,\n        toolbar: {\n          show: false\n        },\n        background: 'transparent',\n        zoom: {\n          enabled: false\n        }\n      },\n      dataLabels: {\n        enabled: true,\n        formatter: function (val) {\n          if (val >= 1) {\n            return '$' + val.toFixed(2) + 'k';\n          } else if (val >= 0.1) {\n            return '$' + val.toFixed(2) + 'k';\n          } else if (val <= -0.1) {\n            return '-$' + Math.abs(val).toFixed(2) + 'k';\n          } else {\n            return '$' + val.toFixed(0) + 'k';\n          }\n        },\n        style: {\n          fontSize: '14px',\n          colors: ['#333'],\n          fontWeight: '500'\n        },\n        offsetY: -15,\n        background: {\n          enabled: false\n        },\n        dropShadow: {\n          enabled: false\n        }\n      },\n      stroke: {\n        curve: 'straight',\n        width: 3\n      },\n      fill: {\n        type: 'solid'\n      },\n      markers: {\n        size: 5,\n        strokeColors: '#fff',\n        strokeWidth: 2,\n        colors: netIncomeData.map(val => val >= 0 ? '#1E7C8C' : '#d70015'),\n        hover: {\n          size: 7\n        },\n        discrete: netIncomeData.map((val, index) => ({\n          seriesIndex: 0,\n          dataPointIndex: index,\n          fillColor: val >= 0 ? '#1E7C8C' : '#d70015',\n          strokeColor: '#fff',\n          size: 5\n        }))\n      },\n      xaxis: {\n        categories: categories,\n        labels: {\n          style: {\n            colors: '#666',\n            fontSize: '14px'\n          }\n        },\n        axisBorder: {\n          show: false\n        },\n        axisTicks: {\n          show: false\n        }\n      },\n      yaxis: {\n        show: false\n      },\n      colors: ['#1E7C8C'],\n      // Default color for positive values\n      plotOptions: {\n        line: {\n          colors: {\n            threshold: 0,\n            colorAboveThreshold: '#1E7C8C',\n            // Teal for positive values\n            colorBelowThreshold: '#d70015' // Red for negative values\n          }\n        }\n      },\n      tooltip: {\n        y: {\n          formatter: function (val) {\n            if (val >= 1) {\n              return '$' + val.toFixed(2) + ' k';\n            } else if (val >= 0.1) {\n              return '$' + val.toFixed(2) + ' k';\n            } else if (val <= -0.1) {\n              return '-$' + Math.abs(val).toFixed(2) + ' k';\n            } else {\n              return '$' + val.toFixed(0) + ' k';\n            }\n          }\n        }\n      },\n      grid: {\n        show: false,\n        padding: {\n          left: 25,\n          right: 25,\n          top: 25,\n          bottom: 0\n        }\n      },\n      annotations: {\n        yaxis: [{\n          y: 0,\n          borderColor: '#666',\n          borderWidth: 1,\n          strokeDashArray: 0,\n          opacity: 0.8\n        }]\n      }\n    };\n\n    // 3. Gross Profit Margin Chart\n    const grossProfitOptions = {\n      series: [{\n        name: \"Gross Profit Margin\",\n        data: grossProfitMarginData\n      }],\n      chart: {\n        type: \"bar\",\n        height: 350,\n        toolbar: {\n          show: false\n        },\n        background: \"transparent\"\n      },\n      plotOptions: {\n        bar: {\n          horizontal: false,\n          columnWidth: \"55%\",\n          endingShape: \"rounded\",\n          dataLabels: {\n            position: \"top\"\n          }\n        }\n      },\n      dataLabels: {\n        enabled: true,\n        position: 'top',\n        formatter: function (val) {\n          if (val >= 1) {\n            return val.toFixed(2) + '%';\n          } else if (val >= 0.1) {\n            return val.toFixed(2) + '%';\n          } else if (val <= -0.1) {\n            return '-' + Math.abs(val).toFixed(2) + '%';\n          } else {\n            return val.toFixed(0) + '%';\n          }\n        },\n        offsetY: -20,\n        style: {\n          fontSize: \"14px\",\n          colors: [\"#333\"],\n          fontWeight: \"500\"\n        }\n      },\n      stroke: {\n        show: true,\n        width: 2,\n        colors: [\"transparent\"]\n      },\n      xaxis: {\n        categories: categories,\n        labels: {\n          style: {\n            colors: \"#666\",\n            fontSize: \"14px\"\n          }\n        },\n        axisBorder: {\n          show: false\n        },\n        axisTicks: {\n          show: false\n        }\n      },\n      yaxis: {\n        show: false,\n        min: Math.min(...grossProfitMarginData) < 0 ? Math.min(...grossProfitMarginData) * 1.1 : 0,\n        max: Math.max(...grossProfitMarginData) * 1.2\n      },\n      fill: {\n        opacity: 1\n      },\n      colors: [\"#4a4a9a\"],\n      tooltip: {\n        y: {\n          formatter: function (val) {\n            if (val === null || val === undefined || isNaN(val)) return \"N/A\";\n            return val.toFixed(2) + \"%\";\n          }\n        }\n      },\n      grid: {\n        show: false,\n        padding: {\n          left: 25,\n          right: 25,\n          top: 20,\n          bottom: 0\n        }\n      }\n    };\n\n    // 4. Net Profit Margin Chart\n    const netProfitMarginOptions = {\n      series: [{\n        name: 'Net Profit Margin',\n        data: netProfitMarginRaw\n      }],\n      chart: {\n        type: 'line',\n        height: 300,\n        toolbar: {\n          show: false\n        },\n        background: 'transparent',\n        zoom: {\n          enabled: false\n        }\n      },\n      dataLabels: {\n        enabled: true,\n        formatter: function (val) {\n          if (val >= 1) {\n            return val.toFixed(2) + '%';\n          } else if (val >= 0.1) {\n            return val.toFixed(2) + '%';\n          } else if (val <= -0.1) {\n            return '-' + Math.abs(val).toFixed(2) + '%';\n          } else {\n            return val.toFixed(0) + '%';\n          }\n        },\n        style: {\n          fontSize: '14px',\n          colors: ['#333'],\n          fontWeight: '500'\n        },\n        offsetY: -15,\n        background: {\n          enabled: false\n        },\n        dropShadow: {\n          enabled: false\n        }\n      },\n      stroke: {\n        curve: 'straight',\n        width: 3\n      },\n      fill: {\n        type: 'solid'\n      },\n      markers: {\n        size: 5,\n        strokeColors: '#fff',\n        strokeWidth: 2,\n        colors: netProfitMarginRaw.map(val => val >= 0 ? '#1E7C8C' : '#d70015'),\n        hover: {\n          size: 7\n        },\n        discrete: netProfitMarginRaw.map((val, index) => ({\n          seriesIndex: 0,\n          dataPointIndex: index,\n          fillColor: val >= 0 ? '#1E7C8C' : '#d70015',\n          strokeColor: '#fff',\n          size: 5\n        }))\n      },\n      xaxis: {\n        categories: categories,\n        labels: {\n          style: {\n            colors: '#666',\n            fontSize: '14px'\n          }\n        },\n        axisBorder: {\n          show: false\n        },\n        axisTicks: {\n          show: false\n        }\n      },\n      yaxis: {\n        show: false\n      },\n      colors: ['#1E7C8C'],\n      // Default line color\n      plotOptions: {\n        line: {\n          colors: {\n            threshold: 0,\n            colorAboveThreshold: '#1E7C8C',\n            colorBelowThreshold: '#d70015'\n          }\n        }\n      },\n      tooltip: {\n        y: {\n          formatter: function (val) {\n            if (val >= 1) {\n              return val.toFixed(2) + '%';\n            } else if (val >= 0.1) {\n              return val.toFixed(2) + '%';\n            } else if (val <= -0.1) {\n              return '-' + Math.abs(val).toFixed(2) + '%';\n            } else {\n              return val.toFixed(0) + '%';\n            }\n          }\n        }\n      },\n      grid: {\n        show: false,\n        padding: {\n          left: 25,\n          right: 25,\n          top: 25,\n          bottom: 0\n        }\n      },\n      annotations: {\n        yaxis: [{\n          y: 0,\n          borderColor: '#666',\n          borderWidth: 1,\n          strokeDashArray: 0,\n          opacity: 0.8\n        }]\n      }\n    };\n\n    // Clear existing charts\n    [stackedColumnRef, netIncomeRef, grossProfitRef, netProfitMarginRef].forEach(ref => {\n      if (ref.current) {\n        ref.current.innerHTML = \"\";\n      }\n    });\n\n    // Get enabled charts and assign chart options\n    const enabledCharts = getEnabledCharts();\n\n    // Assign chart options to enabled charts\n    enabledCharts.forEach(chart => {\n      switch (chart.key) {\n        case 'incomeSummary':\n          chart.options = stackedColumnOptions;\n          chart.name = 'Stacked Column';\n          break;\n        case 'netIncome':\n          chart.options = netIncomeOptions;\n          chart.name = 'Net Income';\n          break;\n        case 'grossProfitMargin':\n          chart.options = grossProfitOptions;\n          chart.name = 'Gross Profit Margin';\n          break;\n        case 'netProfitMargin':\n          chart.options = netProfitMarginOptions;\n          chart.name = 'Net Profit Margin';\n          break;\n      }\n    });\n\n    // Clear existing charts before rendering new ones\n    const clearAndRenderChart = (ref, options, chartName) => {\n      if (ref.current) {\n        // Clear any existing chart\n        ref.current.innerHTML = '';\n\n        // Wait a tick before rendering to ensure DOM is cleared\n        setTimeout(() => {\n          if (ref.current) {\n            try {\n              const chart = new ApexCharts(ref.current, options);\n              chart.render();\n\n              // Store chart instances globally for export\n              if (chartName === \"Stacked Column\") {\n                window.stackedColumnChart = chart;\n              } else if (chartName === \"Net Income\") {\n                window.netIncomeChart = chart;\n              } else if (chartName === \"Gross Profit Margin\") {\n                window.grossProfitChart = chart;\n              } else if (chartName === \"Net Profit Margin\") {\n                window.netProfitMarginChart = chart;\n              }\n            } catch (error) {\n              console.error(`FiscalYear - Error rendering ${chartName} chart:`, error);\n              // Show error message in chart container\n              ref.current.innerHTML = `<div class=\"flex items-center justify-center h-48 text-gray-500\">Error loading ${chartName} chart</div>`;\n            }\n          }\n        }, 10);\n      }\n    };\n\n    // Helper function to check if data array has meaningful values (not all zeros)\n    const hasMeaningfulData = dataArray => {\n      return dataArray && dataArray.length > 0 && dataArray.some(val => parseFloat(val) !== 0);\n    };\n\n    // Render charts with fallback for empty/zero data\n    enabledCharts.forEach(({\n      ref,\n      options,\n      name,\n      key\n    }) => {\n      if (ref.current) {\n        let hasData = false;\n\n        // Check if chart has meaningful data based on chart type\n        if (key === 'incomeSummary') {\n          const monthlyData = (fiscalData === null || fiscalData === void 0 ? void 0 : fiscalData.monthlyPerformanceBreakDown) || [];\n          hasData = monthlyData.some(item => {\n            const income = parseFloat(item.totalIncome || 0);\n            const cogs = parseFloat(item.totalCOGS || 0);\n            const expenses = parseFloat(item.totalExpenses || 0);\n            return income !== 0 || cogs !== 0 || expenses !== 0;\n          });\n        } else if (key === 'netIncome') {\n          const monthlyData = (fiscalData === null || fiscalData === void 0 ? void 0 : fiscalData.monthlyPerformanceBreakDown) || [];\n          const netIncomeData = monthlyData.map(item => parseFloat(item.netIncome || 0));\n          hasData = hasMeaningfulData(netIncomeData);\n        } else if (key === 'grossProfitMargin') {\n          var _fiscalData$grossProf;\n          const grossProfitData = (fiscalData === null || fiscalData === void 0 ? void 0 : (_fiscalData$grossProf = fiscalData.grossProfitMargin) === null || _fiscalData$grossProf === void 0 ? void 0 : _fiscalData$grossProf.map(item => parseFloat(item.grossProfitMargin || 0))) || [];\n          hasData = hasMeaningfulData(grossProfitData);\n        } else if (key === 'netProfitMargin') {\n          var _fiscalData$nerProfit;\n          const netProfitData = (fiscalData === null || fiscalData === void 0 ? void 0 : (_fiscalData$nerProfit = fiscalData.nerProfitMargin) === null || _fiscalData$nerProfit === void 0 ? void 0 : _fiscalData$nerProfit.map(item => parseFloat(item.nerProfitMargin || 0))) || [];\n          hasData = hasMeaningfulData(netProfitData);\n        }\n        if (hasData) {\n          clearAndRenderChart(ref, options, name);\n        } else {\n          ref.current.innerHTML = `<div class=\"flex items-center justify-center h-64 text-gray-500\">No meaningful ${name.toLowerCase()} data available</div>`;\n        }\n      }\n    });\n\n    // Clear all chart containers that are not being used\n    [stackedColumnRef, netIncomeRef, grossProfitRef, netProfitMarginRef].forEach(ref => {\n      if (ref.current && !enabledCharts.some(chart => chart.ref === ref)) {\n        ref.current.innerHTML = \"\";\n      }\n    });\n  };\n  const formatHeaderPeriod = (startYear, startMonth) => {\n    const monthNames = [\"January\", \"February\", \"March\", \"April\", \"May\", \"June\", \"July\", \"August\", \"September\", \"October\", \"November\", \"December\"];\n    if (!startYear || !startMonth) {\n      return \" \"; // fallback\n    }\n    const startMonthName = monthNames[startMonth - 1]; // Convert 1-based to 0-based index\n\n    return `${startMonthName} ${startYear}`;\n  };\n  const formatHeaderStyle = () => {\n    const style = {\n      ...headerTextStyle\n    };\n    if (style.fontSize) {\n      const fontSize = parseInt(style.fontSize);\n      style.fontSize = `${fontSize / 2}px`;\n    }\n    return style;\n  };\n  const formatCompanyName = companyName => {\n    if (!companyName) return '';\n    if (companyName.length > 15) {\n      return companyName.substring(0, 15) + '...';\n    }\n    return companyName;\n  };\n\n  // Calculate YTD totals from monthly data\n  const calculateYTDTotals = () => {\n    if (!fiscalData.monthlyPerformanceBreakDown) return {};\n    return fiscalData.monthlyPerformanceBreakDown.reduce((totals, month) => {\n      totals.totalIncome += parseFloat(month.totalIncome || 0);\n      totals.totalCOGS += parseFloat(month.totalCOGS || 0);\n      totals.totalExpenses += parseFloat(month.totalExpenses || 0);\n      return totals;\n    }, {\n      totalIncome: 0,\n      totalCOGS: 0,\n      totalExpenses: 0\n    });\n  };\n\n  // Function to determine which charts should be rendered and their order\n  const getEnabledCharts = () => {\n    const allCharts = [{\n      key: 'incomeSummary',\n      title: 'Monthly Performance Breakdown',\n      ref: stackedColumnRef,\n      options: null,\n      // Will be set in initializeCharts\n      hasData: () => {\n        if (!(fiscalData !== null && fiscalData !== void 0 && fiscalData.monthlyPerformanceBreakDown)) return false;\n        const monthlyData = fiscalData.monthlyPerformanceBreakDown;\n        return monthlyData.some(item => {\n          const income = parseFloat(item.totalIncome || 0);\n          const cogs = parseFloat(item.totalCOGS || 0);\n          const expenses = parseFloat(item.totalExpenses || 0);\n          return income > 0 || cogs > 0 || expenses > 0;\n        });\n      }\n    }, {\n      key: 'netIncome',\n      title: 'Net Income/(Loss)',\n      ref: netIncomeRef,\n      options: null,\n      hasData: () => {\n        if (!(fiscalData !== null && fiscalData !== void 0 && fiscalData.netIncomeLoss)) return false;\n        return fiscalData.netIncomeLoss.some(item => parseFloat(item.netIncomeLoss || 0) !== 0);\n      }\n    }, {\n      key: 'grossProfitMargin',\n      title: 'Gross Profit Margin',\n      ref: grossProfitRef,\n      options: null,\n      hasData: () => {\n        if (!(fiscalData !== null && fiscalData !== void 0 && fiscalData.monthlyGrossProfitMargin)) return false;\n        return fiscalData.monthlyGrossProfitMargin.some(item => parseFloat(item.Gross_Profit_Margin || 0) !== 0);\n      },\n      hasDescription: true,\n      description: {\n        title: 'Gross Profit Margin',\n        content: 'Is a share of Gross Profit in Total Income or the profit left for covering operating and other expenses. A good Gross Profit Margin is high enough to cover overhead and leave a reasonable Net Profit.'\n      }\n    }, {\n      key: 'netProfitMargin',\n      title: 'Net Profit Margin',\n      ref: netProfitMarginRef,\n      options: null,\n      hasData: () => {\n        if (!(fiscalData !== null && fiscalData !== void 0 && fiscalData.nerProfitMargin)) return false;\n        return fiscalData.nerProfitMargin.some(item => parseFloat(item.nerProfitMargin || 0) !== 0);\n      },\n      hasDescription: true,\n      description: {\n        title: 'Net Profit Margin',\n        content: 'Shows the profit earned per dollar of income. A 10% Net Profit Margin is considered an excellent ratio. If your company has a low Net Profit Margin you are making very little profit after all costs. That implies the revenue is getting eaten up by expenses. It also increases the risk your firm will be unable to meet obligations. With a low margin, a sudden dip in sales over the next month or year could turn your company unprofitable. A high margin indicates your company has solid competitive advantages.'\n      }\n    }];\n\n    // Filter charts based on settings and data availability\n    return allCharts.filter(chart => shouldDisplayChart(chart.key) && chart.hasData());\n  };\n  const ytdTotals = calculateYTDTotals();\n  // Fix floating point precision for net profit calculation\n  const netProfit = Math.round((ytdTotals.totalIncome - ytdTotals.totalCOGS - ytdTotals.totalExpenses) * 100) / 100;\n\n  // Get enabled charts for dynamic layout\n  const enabledCharts = getEnabledCharts();\n\n  // Split charts between upper and lower divs\n  // Upper div can hold up to 2 charts, lower div gets the rest\n  const upperDivCharts = enabledCharts.slice(0, 2);\n  const lowerDivCharts = enabledCharts.slice(2);\n\n  // Helper function to render a chart component\n  const renderChart = chart => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white p-6 border-b-4 border-blue-900 mb-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-2xl font-semibold text-teal-600 mb-5\",\n      style: subHeadingTextStyle,\n      children: chart.title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 950,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: chart.ref\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 956,\n      columnNumber: 7\n    }, this), chart.hasDescription && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-5 text-xl text-gray-600 leading-relaxed rounded-lg pb-5\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-teal-600 text-2xl\",\n        style: {\n          ...subHeadingTextStyle,\n          fontWeight: \"lighter\"\n        },\n        children: chart.description.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 959,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: contentTextStyle,\n        children: chart.description.content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 965,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 958,\n      columnNumber: 9\n    }, this)]\n  }, chart.key, true, {\n    fileName: _jsxFileName,\n    lineNumber: 949,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-5\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl h-[420mm] mx-auto bg-white flex flex-col gap-8 p-10 mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"component-header flex items-center justify-between gap-4  border-b-4 border-blue-900 pb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-bold text-gray-800 m-0\",\n          style: headerTextStyle,\n          children: \"Current Fiscal Year\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 978,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 m-0\",\n          style: formatHeaderStyle(),\n          children: [formatHeaderPeriod(fiscalData === null || fiscalData === void 0 ? void 0 : fiscalData.FYStartYear, fiscalData === null || fiscalData === void 0 ? void 0 : fiscalData.FYStartMonth), \" | \", formatCompanyName(fiscalData === null || fiscalData === void 0 ? void 0 : fiscalData.companyName)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 984,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 977,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"metrics-flex grid grid-cols-4 gap-5 pb-8 border-b-4 border-blue-900\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4 text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xl mb-1\",\n            style: {\n              ...contentTextStyle,\n              fontSize: '20px',\n              color: \"#4b5562\"\n            },\n            children: \"YTD Total Income\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 992,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-4xl font-bold text-gray-600 m-0\",\n            children: formatNumber(ytdTotals.totalIncome)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 995,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 991,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4 text-center\",\n          style: {\n            backgroundColor: \"#d2e9ea\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xl mb-1\",\n            style: {\n              ...contentTextStyle,\n              fontSize: '20px',\n              color: \"#4b5562\"\n            },\n            children: \"YTD Cost of Goods Sold\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1003,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-4xl font-bold text-gray-600 m-0\",\n            children: formatNumber(ytdTotals.totalCOGS)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1006,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 999,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4 text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xl mb-1\",\n            style: {\n              ...contentTextStyle,\n              fontSize: '20px',\n              color: \"#4b5562\"\n            },\n            children: \"YTD Total Expense\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1011,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-4xl font-bold text-gray-600 m-0\",\n            children: formatNumber(ytdTotals.totalExpenses)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1014,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1010,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4 text-center\",\n          style: {\n            backgroundColor: \"#d2e9ea\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xl mb-1\",\n            style: {\n              ...contentTextStyle,\n              fontSize: '20px',\n              color: \"#4b5562\"\n            },\n            children: \"YTD Net Profit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1022,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-4xl font-bold text-gray-600 m-0\",\n            children: formatNumber(netProfit)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1025,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1018,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 990,\n        columnNumber: 9\n      }, this), upperDivCharts.map(chart => renderChart(chart))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 975,\n      columnNumber: 7\n    }, this), lowerDivCharts.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl h-[420mm] mx-auto bg-white flex flex-col gap-10 p-10\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"component-header flex items-center justify-between gap-4 mb-8 border-b-4 border-blue-900 pt-2 pb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-bold text-gray-800 m-0\",\n          style: headerTextStyle,\n          children: \"Current Fiscal Year\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1040,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 m-0\",\n          style: formatHeaderStyle(),\n          children: [formatHeaderPeriod(fiscalData === null || fiscalData === void 0 ? void 0 : fiscalData.FYStartYear, fiscalData === null || fiscalData === void 0 ? void 0 : fiscalData.FYStartMonth), \" | \", formatCompanyName(fiscalData === null || fiscalData === void 0 ? void 0 : fiscalData.companyName)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1046,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1039,\n        columnNumber: 11\n      }, this), lowerDivCharts.map(chart => renderChart(chart))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1038,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 974,\n    columnNumber: 5\n  }, this);\n};\n_s(FiscalYearDashboard, \"TNVw/Nsc0yyRtbTo+qU1kNvpirA=\");\n_c = FiscalYearDashboard;\nexport default FiscalYearDashboard;\nvar _c;\n$RefreshReg$(_c, \"FiscalYearDashboard\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "Apex<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "FiscalYearDashboard", "headerTextStyle", "headingTextStyle", "subHeadingTextStyle", "contentTextStyle", "fiscalData", "contentSettings", "_s", "stackedColumnRef", "netIncomeRef", "grossProfitRef", "netProfitMarginRef", "isDataLoaded", "hasMonthlyData", "monthlyPerformanceBreakDown", "Array", "isArray", "length", "hasGrossProfitMargin", "monthlyGrossProfitMargin", "hasNetProfitMargin", "nerProfitMargin", "hasNetIncomeData", "netIncomeLoss", "hasAnyUsableData", "monthlyData", "hasMeaningfulMonthlyData", "some", "item", "income", "parseFloat", "totalIncome", "cogs", "totalCOGS", "expenses", "totalExpenses", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chart<PERSON>ey", "chartSettings", "for<PERSON>ach", "ref", "current", "innerHTML", "initializeCharts", "formatMonthYear", "year", "month", "monthNames", "String", "slice", "formatNumber", "num", "roundedNum", "Math", "round", "isNegative", "absNum", "abs", "toString", "toFixed", "suffixes", "value", "suffix", "i", "formatted", "cleanFormatted", "endsWith", "categories", "map", "netProfitMarginMap", "Map", "key", "set", "grossProfitMarginMap", "Gross_Profit_Margin", "percentage", "netIncomeMap", "incomeData", "cogsData", "expenseData", "grossProfitMarginData", "get", "console", "log", "netProfitMarginRaw", "netIncomeData", "stackedColumnOptions", "series", "name", "type", "data", "chart", "height", "stacked", "toolbar", "show", "background", "dataLabels", "enabled", "enabledOnSeries", "formatter", "val", "opts", "undefined", "isNaN", "absVal", "style", "fontSize", "colors", "fontWeight", "offsetY", "dropShadow", "stroke", "width", "curve", "plotOptions", "bar", "columnWidth", "total", "color", "fill", "opacity", "labels", "markers", "size", "strokeColors", "strokeWidth", "fillOpacity", "hover", "xaxis", "axisBorder", "axisTicks", "yaxis", "min", "max", "maxStacked", "max<PERSON><PERSON><PERSON>", "legend", "position", "horizontalAlign", "radius", "useSeriesColors", "itemMargin", "horizontal", "vertical", "onItemClick", "toggleDataSeries", "onItemHover", "highlightDataSeries", "tooltip", "shared", "intersect", "custom", "seriesIndex", "dataPointIndex", "w", "expense", "category", "globals", "grid", "padding", "left", "right", "top", "bottom", "annotations", "y", "borderColor", "label", "netIncomeOptions", "zoom", "discrete", "index", "fillColor", "strokeColor", "line", "threshold", "colorAboveThreshold", "colorBelowThreshold", "borderWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grossProfitOptions", "endingShape", "netProfitMarginOptions", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options", "clearAndRender<PERSON>hart", "chartName", "setTimeout", "render", "window", "stackedColumnChart", "netIncomeChart", "grossProfitChart", "netProfitMarginChart", "error", "hasMeaningfulData", "dataArray", "hasData", "netIncome", "_fiscalData$grossProf", "grossProfitData", "grossProfitMargin", "_fiscalData$nerProfit", "netProfitData", "toLowerCase", "formatHeaderPeriod", "startYear", "startMonth", "startMonthName", "formatHeaderStyle", "parseInt", "formatCompanyName", "companyName", "substring", "calculateYTDTotals", "reduce", "totals", "all<PERSON>hart<PERSON>", "title", "hasDescription", "description", "content", "filter", "ytdTotals", "netProfit", "upperDivCharts", "lowerDiv<PERSON><PERSON>s", "<PERSON><PERSON><PERSON>", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "FYStartYear", "FYStartMonth", "backgroundColor", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Valisights/valisight-app/src/pages/reports/ReportPages/FiscalYear.jsx"], "sourcesContent": ["import React, { useEffect, useRef } from \"react\";\r\nimport ApexCharts from \"apexcharts\";\r\n\r\nconst FiscalYearDashboard = ({\r\n  headerTextStyle = {},\r\n  headingTextStyle = {},\r\n  subHeadingTextStyle = {},\r\n  contentTextStyle = {},\r\n  fiscalData = null,\r\n  contentSettings = null, // Add contentSettings prop\r\n}) => {\r\n  const stackedColumnRef = useRef(null);\r\n  const netIncomeRef = useRef(null);\r\n  const grossProfitRef = useRef(null);\r\n  const netProfitMarginRef = useRef(null);\r\n\r\n  // Enhanced data validation function - more lenient approach\r\n  const isDataLoaded = () => {\r\n    if (!fiscalData) {\r\n      return false;\r\n    }\r\n\r\n    // Check if at least one of the required data arrays exists and has content\r\n    const hasMonthlyData = fiscalData.monthlyPerformanceBreakDown &&\r\n      Array.isArray(fiscalData.monthlyPerformanceBreakDown) &&\r\n      fiscalData.monthlyPerformanceBreakDown.length > 0;\r\n\r\n    const hasGrossProfitMargin = fiscalData.monthlyGrossProfitMargin &&\r\n      Array.isArray(fiscalData.monthlyGrossProfitMargin) &&\r\n      fiscalData.monthlyGrossProfitMargin.length > 0;\r\n\r\n    const hasNetProfitMargin = fiscalData.nerProfitMargin &&\r\n      Array.isArray(fiscalData.nerProfitMargin) &&\r\n      fiscalData.nerProfitMargin.length > 0;\r\n\r\n    const hasNetIncomeData = fiscalData.netIncomeLoss &&\r\n      Array.isArray(fiscalData.netIncomeLoss) &&\r\n      fiscalData.netIncomeLoss.length > 0;\r\n   // We'll handle missing other data gracefully in the charts\r\n    return hasMonthlyData;\r\n  };\r\n\r\n  // Function to check if we have any meaningful data (not all zeros)\r\n  const hasAnyUsableData = () => {\r\n    if (!fiscalData?.monthlyPerformanceBreakDown) {\r\n      return false;\r\n    }\r\n\r\n    const monthlyData = fiscalData.monthlyPerformanceBreakDown;\r\n\r\n    // Check if any monthly data has meaningful values\r\n    const hasMeaningfulMonthlyData = monthlyData.some(item => {\r\n      const income = parseFloat(item.totalIncome || 0);\r\n      const cogs = parseFloat(item.totalCOGS || 0);\r\n      const expenses = parseFloat(item.totalExpenses || 0);\r\n\r\n      return income > 0 || cogs > 0 || expenses > 0;\r\n    });\r\n    return hasMeaningfulMonthlyData;\r\n  };\r\n\r\n  // Function to check if a chart should be displayed based on content settings\r\n  const shouldDisplayChart = (chartKey) => {\r\n    if (!contentSettings?.chartSettings) return true; // Default to true if no settings\r\n    return contentSettings.chartSettings[chartKey] === true;\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (isDataLoaded()) {\r\n      // Clear charts first\r\n      [stackedColumnRef, netIncomeRef, grossProfitRef, netProfitMarginRef].forEach((ref) => {\r\n        if (ref.current) {\r\n          ref.current.innerHTML = \"\";\r\n        }\r\n      });\r\n      // Initialize charts with new data\r\n      initializeCharts();\r\n    }\r\n  }, [fiscalData, contentSettings]); // Add contentSettings to dependency array\r\n\r\n  const formatMonthYear = (year, month) => {\r\n    const monthNames = [\r\n      \"Jan\",\r\n      \"Feb\",\r\n      \"Mar\",\r\n      \"Apr\",\r\n      \"May\",\r\n      \"Jun\",\r\n      \"Jul\",\r\n      \"Aug\",\r\n      \"Sep\",\r\n      \"Oct\",\r\n      \"Nov\",\r\n      \"Dec\",\r\n    ];\r\n    return `${monthNames[month - 1]} ${String(year).slice(-2)}`;\r\n  };\r\n\r\n  function formatNumber(num) {\r\n    // Round to 2 decimal places to fix floating point precision issues\r\n    const roundedNum = Math.round(num * 100) / 100;\r\n    const isNegative = roundedNum < 0;\r\n    const absNum = Math.abs(roundedNum);\r\n\r\n    // For numbers under 10k, show with appropriate decimal places (no suffix)\r\n    if (absNum < 1000) {\r\n      return (isNegative ? \"-\" : \"\") + (absNum % 1 === 0 ? absNum.toString() : absNum.toFixed(2));\r\n    }\r\n\r\n    const suffixes = [\r\n      { value: 1e12, suffix: \"T\" },\r\n      { value: 1e9, suffix: \"B\" },\r\n      { value: 1e6, suffix: \"M\" },\r\n      { value: 1e3, suffix: \"K\" },\r\n    ];\r\n\r\n    for (let i = 0; i < suffixes.length; i++) {\r\n      if (absNum >= suffixes[i].value) {\r\n        const formatted = (absNum / suffixes[i].value).toFixed(1);\r\n        const cleanFormatted = formatted.endsWith(\".0\")\r\n          ? formatted.slice(0, -2)\r\n          : formatted;\r\n        return (isNegative ? \"-\" : \"\") + cleanFormatted + suffixes[i].suffix;\r\n      }\r\n    }\r\n\r\n    return (isNegative ? \"-\" : \"\") + roundedNum.toString();\r\n  }\r\n\r\n  const initializeCharts = () => {\r\n    if (!fiscalData?.monthlyPerformanceBreakDown) return;\r\n\r\n    const monthlyData = fiscalData.monthlyPerformanceBreakDown;\r\n\r\n    // Create categories from monthlyPerformanceBreakDown\r\n    const categories = monthlyData.map((item) =>\r\n      formatMonthYear(item.year, item.month)\r\n    );\r\n\r\n    // Create lookup maps with fallback for missing data\r\n    const netProfitMarginMap = new Map();\r\n    if (fiscalData.nerProfitMargin && Array.isArray(fiscalData.nerProfitMargin)) {\r\n      fiscalData.nerProfitMargin.forEach(item => {\r\n        const key = `${item.year}-${item.month}`;\r\n        const value = parseFloat(item.nerProfitMargin) || 0;\r\n        netProfitMarginMap.set(key, value);\r\n      });\r\n    }\r\n\r\n    const grossProfitMarginMap = new Map();\r\n    if (fiscalData.monthlyGrossProfitMargin && Array.isArray(fiscalData.monthlyGrossProfitMargin)) {\r\n      fiscalData.monthlyGrossProfitMargin.forEach(item => {\r\n        const key = `${item.year}-${item.month}`;\r\n        const value = parseFloat(item.Gross_Profit_Margin);\r\n        const percentage = value > 1 ? value : value * 100;\r\n        grossProfitMarginMap.set(key, percentage || 0);\r\n      });\r\n    }\r\n\r\n    const netIncomeMap = new Map();\r\n    if (fiscalData.netIncomeLoss && Array.isArray(fiscalData.netIncomeLoss)) {\r\n      fiscalData.netIncomeLoss.forEach(item => {\r\n        const key = `${item.year}-${item.month}`;\r\n        const value = parseFloat(item.netIncomeLoss) / 1000 || 0;\r\n        netIncomeMap.set(key, value);\r\n      });\r\n    }\r\n\r\n    // Create aligned data arrays\r\n    const incomeData = monthlyData.map((item) => parseFloat(item.totalIncome) / 1000 || 0);\r\n    const cogsData = monthlyData.map((item) => parseFloat(item.totalCOGS) / 1000 || 0);\r\n    const expenseData = monthlyData.map((item) => parseFloat(item.totalExpenses) / 1000 || 0);\r\n\r\n    const grossProfitMarginData = monthlyData.map((item) => {\r\n      const key = `${item.year}-${item.month}`;\r\n      return grossProfitMarginMap.get(key) || 0;\r\n    });\r\n\r\n    console.log(\"gross profit margin\", grossProfitMarginData);\r\n\r\n    const netProfitMarginRaw = monthlyData.map((item) => {\r\n      const key = `${item.year}-${item.month}`;\r\n      return netProfitMarginMap.get(key) || 0;\r\n    });\r\n\r\n    const netIncomeData = monthlyData.map((item) => {\r\n      const key = `${item.year}-${item.month}`;\r\n      return netIncomeMap.get(key) || 0;\r\n    });\r\n\r\n    // 1. Stacked Column Chart (incomeSummary)\r\nconst stackedColumnOptions = {\r\n  series: [\r\n    { name: \"Income\", type: \"line\", data: incomeData },\r\n    { name: \"Expense\", type: \"column\", data: expenseData },\r\n    { name: \"Cost of Goods Sold\", type: \"column\", data: cogsData },\r\n  ],\r\n  chart: {\r\n    height: 450,\r\n    type: \"bar\",\r\n    stacked: true,\r\n    toolbar: { show: false },\r\n    background: \"transparent\",\r\n  },\r\n  dataLabels: {\r\n    enabled: true,\r\n    enabledOnSeries: [0, 1, 2], // Show labels on all three series: Income, Expense, and COGS\r\n    formatter: function (val, opts) {\r\n      if (val === null || val === undefined || isNaN(val)) return \"\";\r\n      \r\n      const absVal = Math.abs(val);\r\n      \r\n      if (absVal >= 1000) {\r\n        return '$' + (val / 1000).toFixed(1) + 'm';\r\n      } else if (absVal >= 1) {\r\n        return '$' + val.toFixed(2) + 'k';\r\n      } else {\r\n        return '$' + (val * 1000).toFixed(0);\r\n      }\r\n    },\r\n    style: {\r\n      fontSize: \"14px\",\r\n      colors: [\"#20b2aa\", \"#333\", \"#333\"], // Different colors for each series\r\n      fontWeight: \"500\",\r\n    },\r\n    offsetY: -10,\r\n    background: {\r\n      enabled: false,\r\n    },\r\n    dropShadow: {\r\n      enabled: false,\r\n    },\r\n  },\r\n  stroke: {\r\n    width: [2, 0, 0],\r\n    curve: \"smooth\",\r\n  },\r\n  plotOptions: {\r\n    bar: {\r\n      columnWidth: \"60%\",\r\n      dataLabels: {\r\n        total: {\r\n          enabled: false, // Disable total labels\r\n          offsetY: -20,\r\n          style: {\r\n            fontSize: \"14px\",\r\n            fontWeight: \"500\",\r\n            color: \"#333\",\r\n          },\r\n          formatter: function (val) {\r\n            if (val === null || val === undefined || isNaN(val)) return \"$0\";\r\n            \r\n            const absVal = Math.abs(val);\r\n            \r\n            if (absVal >= 1000) {\r\n              return '$' + (val / 1000).toFixed(1) + 'm';\r\n            } else if (absVal >= 1) {\r\n              return '$' + val.toFixed(1) + 'k';\r\n            } else {\r\n              return '$' + (val * 1000).toFixed(0);\r\n            }\r\n          },\r\n        },\r\n      },\r\n    },\r\n  },\r\n  fill: {\r\n    opacity: [1, 1, 1], // Make all series fully opaque\r\n  },\r\n  labels: categories,\r\n  markers: {\r\n    size: [5, 0, 0],\r\n    fontSize: \"14px\",\r\n    strokeColors: \"#fff\",\r\n    strokeWidth: 2,\r\n    fillOpacity: 1,\r\n    hover: {\r\n      size: 7,\r\n    },\r\n  },\r\n  xaxis: {\r\n    labels: {\r\n      style: {\r\n        colors: \"#666\",\r\n        fontSize: \"14px\",\r\n      },\r\n      offsetY: 15, // Push month labels down by 15px\r\n    },\r\n    axisBorder: {\r\n      show: false,\r\n    },\r\n    axisTicks: {\r\n      show: false,\r\n    },\r\n  },\r\n  yaxis: {\r\n    show: false,\r\n    // More conservative scaling to ensure small COGS values are visible\r\n    min: 0,\r\n    max: function () {\r\n      // Calculate max considering stacked values\r\n      const maxStacked = Math.max(...cogsData.map((cogs, i) => cogs + expenseData[i]));\r\n      const maxIncome = Math.max(...incomeData);\r\n      return Math.max(maxStacked, maxIncome) * 1.2;\r\n    },\r\n  },\r\n  colors: [\"#20b2aa\", \"#ff8a80\", \"#53579f\"], // Colors for Income, Expense, COGS\r\n  legend: {\r\n    position: \"bottom\",\r\n    horizontalAlign: \"center\",\r\n    fontSize: \"14px\",\r\n    fontWeight: \"400\",\r\n    markers: {\r\n      width: 12,\r\n      height: 12,\r\n      radius: 6, // Circular markers\r\n    },\r\n    labels: {\r\n      colors: \"#333\",\r\n      useSeriesColors: false,\r\n    },\r\n    itemMargin: {\r\n      horizontal: 15,\r\n      vertical: 4,\r\n    },\r\n    offsetY: 10,\r\n    onItemClick: {\r\n      toggleDataSeries: true, // Enable clicking to hide/show series\r\n    },\r\n    onItemHover: {\r\n      highlightDataSeries: true, // Enable hover highlighting\r\n    },\r\n  },\r\n  tooltip: {\r\n    shared: true,\r\n    intersect: false,\r\n    // Custom tooltip to clearly show all values\r\n    custom: function ({ series, seriesIndex, dataPointIndex, w }) {\r\n      const income = (series[0][dataPointIndex] * 1000).toFixed(0);\r\n      const expense = (series[1][dataPointIndex] * 1000).toFixed(0);\r\n      const cogs = (series[2][dataPointIndex] * 1000).toFixed(0);\r\n      const category = w.globals.labels[dataPointIndex];\r\n\r\n      return `\r\n        <div style=\"padding: 12px; background: white; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); font-size: 14px;\">\r\n          <div style=\"font-weight: 600; margin-bottom: 8px; color: #333;\">${category}</div>\r\n          <div style=\"display: flex; align-items: center; margin-bottom: 4px;\">\r\n            <div style=\"width: 12px; height: 12px; background: #20b2aa; border-radius: 50%; margin-right: 8px;\"></div>\r\n            <span style=\"color: #333;\">Income: <strong>$${income}</strong></span>\r\n          </div>\r\n          <div style=\"display: flex; align-items: center; margin-bottom: 4px;\">\r\n            <div style=\"width: 12px; height: 12px; background: #ff8a80; border-radius: 50%; margin-right: 8px;\"></div>\r\n            <span style=\"color: #333;\">Expense: <strong>$${expense}</strong></span>\r\n          </div>\r\n          <div style=\"display: flex; align-items: center; margin-bottom: 4px; ${cogs == 0 ? 'opacity: 0.6;' : ''}\">\r\n            <div style=\"width: 12px; height: 12px; background: #4361ee; border-radius: 50%; margin-right: 8px;\"></div>\r\n            <span style=\"color: #333;\">COGS: <strong>$${cogs}</strong> ${cogs == 0 ? '(No data)' : ''}</span>\r\n          </div>\r\n          <div style=\"margin-top: 8px; padding-top: 8px; border-top: 1px solid #eee; font-size: 12px; color: #666;\">\r\n            Total Costs: <strong>$${(parseFloat(expense) + parseFloat(cogs)).toFixed(0)}</strong>\r\n          </div>\r\n        </div>\r\n      `;\r\n    },\r\n  },\r\n  grid: {\r\n    show: false,\r\n    padding: {\r\n      left: 25,\r\n      right: 25,\r\n      top: 20,\r\n      bottom: 30, // Increased bottom padding from 0 to 30\r\n    },\r\n  },\r\n  // Add annotations to highlight if COGS data exists\r\n  annotations: {\r\n    yaxis: cogsData.some(val => val > 0) ? [] : [{\r\n      y: 0,\r\n      borderColor: '#FF4560',\r\n      label: {\r\n        borderColor: '#FF4560',\r\n        style: {\r\n          color: '#fff',\r\n          background: '#FF4560',\r\n        },\r\n      }\r\n    }]\r\n  }\r\n};\r\n\r\n    // 2. Net Income Chart\r\n    const netIncomeOptions = {\r\n      series: [{\r\n        name: 'Net Income',\r\n        data: netIncomeData\r\n      }],\r\n      chart: {\r\n        type: 'line',\r\n        height: 300,\r\n        toolbar: { show: false },\r\n        background: 'transparent',\r\n        zoom : {\r\n          enabled : false\r\n        }\r\n      },\r\n      dataLabels: {\r\n        enabled: true,\r\n        formatter: function (val) {\r\n          if (val >= 1) {\r\n            return '$' + val.toFixed(2) + 'k';\r\n          } else if (val >= 0.1) {\r\n            return '$' + val.toFixed(2) + 'k';\r\n          } else if (val <= -0.1) {\r\n            return '-$' + Math.abs(val).toFixed(2) + 'k';\r\n          } else {\r\n            return '$' + val.toFixed(0) + 'k';\r\n          }\r\n        },\r\n        style: {\r\n          fontSize: '14px',\r\n          colors: ['#333'],\r\n          fontWeight: '500'\r\n        },\r\n        offsetY: -15,\r\n        background: {\r\n          enabled: false\r\n        },\r\n        dropShadow: {\r\n          enabled: false\r\n        }\r\n      },\r\n      stroke: {\r\n        curve: 'straight',\r\n        width: 3\r\n      },\r\n      fill: {\r\n        type: 'solid'\r\n      },\r\n      markers: {\r\n        size: 5,\r\n        strokeColors: '#fff',\r\n        strokeWidth: 2,\r\n        colors: netIncomeData.map(val => val >= 0 ? '#1E7C8C' : '#d70015'),\r\n        hover: {\r\n          size: 7\r\n        },\r\n        discrete: netIncomeData.map((val, index) => ({\r\n          seriesIndex: 0,\r\n          dataPointIndex: index,\r\n          fillColor: val >= 0 ? '#1E7C8C' : '#d70015',\r\n          strokeColor: '#fff',\r\n          size: 5\r\n        }))\r\n      },\r\n      xaxis: {\r\n        categories: categories,\r\n        labels: {\r\n          style: {\r\n            colors: '#666',\r\n            fontSize: '14px',\r\n          }\r\n        },\r\n        axisBorder: { show: false },\r\n        axisTicks: { show: false }\r\n      },\r\n      yaxis: {\r\n        show: false\r\n      },\r\n      colors: ['#1E7C8C'], // Default color for positive values\r\n      plotOptions: {\r\n        line: {\r\n          colors: {\r\n            threshold: 0,\r\n            colorAboveThreshold: '#1E7C8C', // Teal for positive values\r\n            colorBelowThreshold: '#d70015'   // Red for negative values\r\n          }\r\n        }\r\n      },\r\n      tooltip: {\r\n        y: {\r\n          formatter: function (val) {\r\n            if (val >= 1) {\r\n              return '$' + val.toFixed(2) + ' k';\r\n            } else if (val >= 0.1) {\r\n              return '$' + val.toFixed(2) + ' k';\r\n            } else if (val <= -0.1) {\r\n              return '-$' + Math.abs(val).toFixed(2) + ' k';\r\n            } else {\r\n              return '$' + val.toFixed(0) + ' k';\r\n            }\r\n          }\r\n        }\r\n      },\r\n      grid: {\r\n        show: false,\r\n        padding: {\r\n          left: 25,\r\n          right: 25,\r\n          top: 25,\r\n          bottom: 0\r\n        }\r\n      },\r\n      annotations: {\r\n        yaxis: [{\r\n          y: 0,\r\n          borderColor: '#666',\r\n          borderWidth: 1,\r\n          strokeDashArray: 0,\r\n          opacity: 0.8\r\n        }]\r\n      }\r\n    };\r\n\r\n    // 3. Gross Profit Margin Chart\r\n    const grossProfitOptions = {\r\n      series: [{ name: \"Gross Profit Margin\", data: grossProfitMarginData }],\r\n      chart: {\r\n        type: \"bar\",\r\n        height: 350,\r\n        toolbar: { show: false },\r\n        background: \"transparent\",\r\n      },\r\n      plotOptions: {\r\n        bar: {\r\n          horizontal: false,\r\n          columnWidth: \"55%\",\r\n          endingShape: \"rounded\",\r\n          dataLabels: { position: \"top\" },\r\n        },\r\n      },\r\n      dataLabels: {\r\n        enabled: true,\r\n        position: 'top',\r\n       formatter: function (val) {\r\n          if (val >= 1) {\r\n            return val.toFixed(2) + '%';\r\n          } else if (val >= 0.1) {\r\n            return val.toFixed(2) + '%';\r\n          } else if (val <= -0.1) {\r\n            return '-' + Math.abs(val).toFixed(2) + '%';\r\n          } else {\r\n            return val.toFixed(0) + '%';\r\n          }\r\n        },\r\n        offsetY: -20,\r\n        style: { fontSize: \"14px\", colors: [\"#333\"], fontWeight: \"500\" },\r\n      },\r\n      stroke: { show: true, width: 2, colors: [\"transparent\"] },\r\n      xaxis: {\r\n        categories: categories,\r\n        labels: { style: { colors: \"#666\", fontSize: \"14px\" } },\r\n        axisBorder: { show: false },\r\n        axisTicks: { show: false },\r\n      },\r\n      yaxis: {\r\n        show: false,\r\n        min: Math.min(...grossProfitMarginData) < 0 ? Math.min(...grossProfitMarginData) * 1.1 : 0,\r\n        max: Math.max(...grossProfitMarginData) * 1.2,\r\n      },\r\n      fill: { opacity: 1 },\r\n      colors: [\"#4a4a9a\"],\r\n      tooltip: {\r\n        y: {\r\n          formatter: function (val) {\r\n            if (val === null || val === undefined || isNaN(val)) return \"N/A\";\r\n            return val.toFixed(2) + \"%\";\r\n          },\r\n        },\r\n      },\r\n      grid: {\r\n        show: false,\r\n        padding: { left: 25, right: 25, top: 20, bottom: 0 },\r\n      },\r\n    };\r\n\r\n    // 4. Net Profit Margin Chart\r\n    const netProfitMarginOptions = {\r\n      series: [{\r\n        name: 'Net Profit Margin',\r\n        data: netProfitMarginRaw\r\n      }],\r\n      chart: {\r\n        type: 'line',\r\n        height: 300,\r\n        toolbar: { show: false },\r\n        background: 'transparent', \r\n        zoom : {\r\n          enabled : false\r\n        }\r\n      },\r\n      dataLabels: {\r\n        enabled: true,\r\n        formatter: function (val) {\r\n          if (val >= 1) {\r\n            return val.toFixed(2) + '%';\r\n          } else if (val >= 0.1) {\r\n            return val.toFixed(2) + '%';\r\n          } else if (val <= -0.1) {\r\n            return '-' + Math.abs(val).toFixed(2) + '%';\r\n          } else {\r\n            return val.toFixed(0) + '%';\r\n          }\r\n        },\r\n        style: {\r\n          fontSize: '14px',\r\n          colors: ['#333'],\r\n          fontWeight: '500'\r\n        },\r\n        offsetY: -15,\r\n        background: {\r\n          enabled: false\r\n        },\r\n        dropShadow: {\r\n          enabled: false\r\n        }\r\n      },\r\n      stroke: {\r\n        curve: 'straight',\r\n        width: 3\r\n      },\r\n      fill: {\r\n        type: 'solid'\r\n      },\r\n      markers: {\r\n        size: 5,\r\n        strokeColors: '#fff',\r\n        strokeWidth: 2,\r\n        colors: netProfitMarginRaw.map(val => val >= 0 ? '#1E7C8C' : '#d70015'),\r\n        hover: {\r\n          size: 7\r\n        },\r\n        discrete: netProfitMarginRaw.map((val, index) => ({\r\n          seriesIndex: 0,\r\n          dataPointIndex: index,\r\n          fillColor: val >= 0 ? '#1E7C8C' : '#d70015',\r\n          strokeColor: '#fff',\r\n          size: 5\r\n        }))\r\n      },\r\n      xaxis: {\r\n        categories: categories,\r\n        labels: {\r\n          style: {\r\n            colors: '#666',\r\n            fontSize: '14px'\r\n          }\r\n        },\r\n        axisBorder: { show: false },\r\n        axisTicks: { show: false }\r\n      },\r\n      yaxis: {\r\n        show: false\r\n      },\r\n      colors: ['#1E7C8C'], // Default line color\r\n      plotOptions: {\r\n        line: {\r\n          colors: {\r\n            threshold: 0,\r\n            colorAboveThreshold: '#1E7C8C',\r\n            colorBelowThreshold: '#d70015',\r\n          },\r\n        }\r\n      },\r\n      tooltip: {\r\n        y: {\r\n          formatter: function (val) {\r\n            if (val >= 1) {\r\n              return val.toFixed(2) + '%';\r\n            } else if (val >= 0.1) {\r\n              return val.toFixed(2) + '%';\r\n            } else if (val <= -0.1) {\r\n              return '-' + Math.abs(val).toFixed(2) + '%';\r\n            } else {\r\n              return val.toFixed(0) + '%';\r\n            }\r\n          }\r\n        }\r\n      },\r\n      grid: {\r\n        show: false,\r\n        padding: {\r\n          left: 25,\r\n          right: 25,\r\n          top: 25,\r\n          bottom: 0\r\n        }\r\n      },\r\n      annotations: {\r\n        yaxis: [{\r\n          y: 0,\r\n          borderColor: '#666',\r\n          borderWidth: 1,\r\n          strokeDashArray: 0,\r\n          opacity: 0.8\r\n        }]\r\n      }\r\n    };\r\n\r\n    // Clear existing charts\r\n    [stackedColumnRef, netIncomeRef, grossProfitRef, netProfitMarginRef].forEach((ref) => {\r\n      if (ref.current) {\r\n        ref.current.innerHTML = \"\";\r\n      }\r\n    });\r\n\r\n    \r\n\r\n    // Get enabled charts and assign chart options\r\n    const enabledCharts = getEnabledCharts();\r\n\r\n    // Assign chart options to enabled charts\r\n    enabledCharts.forEach(chart => {\r\n      switch (chart.key) {\r\n        case 'incomeSummary':\r\n          chart.options = stackedColumnOptions;\r\n          chart.name = 'Stacked Column';\r\n          break;\r\n        case 'netIncome':\r\n          chart.options = netIncomeOptions;\r\n          chart.name = 'Net Income';\r\n          break;\r\n        case 'grossProfitMargin':\r\n          chart.options = grossProfitOptions;\r\n          chart.name = 'Gross Profit Margin';\r\n          break;\r\n        case 'netProfitMargin':\r\n          chart.options = netProfitMarginOptions;\r\n          chart.name = 'Net Profit Margin';\r\n          break;\r\n      }\r\n    });\r\n\r\n    // Clear existing charts before rendering new ones\r\n    const clearAndRenderChart = (ref, options, chartName) => {\r\n      if (ref.current) {\r\n        // Clear any existing chart\r\n        ref.current.innerHTML = '';\r\n\r\n        // Wait a tick before rendering to ensure DOM is cleared\r\n        setTimeout(() => {\r\n          if (ref.current) {\r\n            try {\r\n              const chart = new ApexCharts(ref.current, options);\r\n              chart.render();\r\n\r\n              // Store chart instances globally for export\r\n              if (chartName === \"Stacked Column\") {\r\n                window.stackedColumnChart = chart;\r\n              } else if (chartName === \"Net Income\") {\r\n                window.netIncomeChart = chart;\r\n              } else if (chartName === \"Gross Profit Margin\") {\r\n                window.grossProfitChart = chart;\r\n              } else if (chartName === \"Net Profit Margin\") {\r\n                window.netProfitMarginChart = chart;\r\n              }\r\n            } catch (error) {\r\n              console.error(`FiscalYear - Error rendering ${chartName} chart:`, error);\r\n              // Show error message in chart container\r\n              ref.current.innerHTML = `<div class=\"flex items-center justify-center h-48 text-gray-500\">Error loading ${chartName} chart</div>`;\r\n            }\r\n          }\r\n        }, 10);\r\n      }\r\n    };\r\n\r\n    // Helper function to check if data array has meaningful values (not all zeros)\r\n    const hasMeaningfulData = (dataArray) => {\r\n      return dataArray && dataArray.length > 0 && dataArray.some(val => parseFloat(val) !== 0);\r\n    };\r\n\r\n    // Render charts with fallback for empty/zero data\r\n    enabledCharts.forEach(({ ref, options, name, key }) => {\r\n      if (ref.current) {\r\n        let hasData = false;\r\n\r\n        // Check if chart has meaningful data based on chart type\r\n        if (key === 'incomeSummary') {\r\n          const monthlyData = fiscalData?.monthlyPerformanceBreakDown || [];\r\n          hasData = monthlyData.some(item => {\r\n            const income = parseFloat(item.totalIncome || 0);\r\n            const cogs = parseFloat(item.totalCOGS || 0);\r\n            const expenses = parseFloat(item.totalExpenses || 0);\r\n            return income !== 0 || cogs !== 0 || expenses !== 0;\r\n          });\r\n        } else if (key === 'netIncome') {\r\n          const monthlyData = fiscalData?.monthlyPerformanceBreakDown || [];\r\n          const netIncomeData = monthlyData.map(item => parseFloat(item.netIncome || 0));\r\n          hasData = hasMeaningfulData(netIncomeData);\r\n        } else if (key === 'grossProfitMargin') {\r\n          const grossProfitData = fiscalData?.grossProfitMargin?.map(item => parseFloat(item.grossProfitMargin || 0)) || [];\r\n          hasData = hasMeaningfulData(grossProfitData);\r\n        } else if (key === 'netProfitMargin') {\r\n          const netProfitData = fiscalData?.nerProfitMargin?.map(item => parseFloat(item.nerProfitMargin || 0)) || [];\r\n          hasData = hasMeaningfulData(netProfitData);\r\n        }\r\n\r\n        if (hasData) {\r\n          clearAndRenderChart(ref, options, name);\r\n        } else {\r\n          ref.current.innerHTML = `<div class=\"flex items-center justify-center h-64 text-gray-500\">No meaningful ${name.toLowerCase()} data available</div>`;\r\n        }\r\n      }\r\n    });\r\n\r\n    // Clear all chart containers that are not being used\r\n    [stackedColumnRef, netIncomeRef, grossProfitRef, netProfitMarginRef].forEach((ref) => {\r\n      if (ref.current && !enabledCharts.some(chart => chart.ref === ref)) {\r\n        ref.current.innerHTML = \"\";\r\n      }\r\n    });\r\n  };\r\n\r\n\r\n  const formatHeaderPeriod = (startYear, startMonth) => {\r\n    const monthNames = [\r\n      \"January\", \"February\", \"March\", \"April\", \"May\", \"June\",\r\n      \"July\", \"August\", \"September\", \"October\", \"November\", \"December\"\r\n    ];\r\n\r\n    if (!startYear || !startMonth) {\r\n      return \" \"; // fallback\r\n    }\r\n\r\n    const startMonthName = monthNames[startMonth - 1]; // Convert 1-based to 0-based index\r\n\r\n    return `${startMonthName} ${startYear}`;\r\n  };\r\n\r\n  const formatHeaderStyle = () => {\r\n    const style = { ...headerTextStyle };\r\n    \r\n    if (style.fontSize) {\r\n      const fontSize = parseInt(style.fontSize);\r\n      style.fontSize = `${fontSize / 2}px`;\r\n    }\r\n    return style;\r\n  };\r\n\r\n  const formatCompanyName = (companyName) => {\r\n    if (!companyName) return '';\r\n\r\n    if (companyName.length > 15) {\r\n      return companyName.substring(0, 15) + '...';\r\n    }\r\n\r\n    return companyName;\r\n  };\r\n\r\n  // Calculate YTD totals from monthly data\r\n  const calculateYTDTotals = () => {\r\n    if (!fiscalData.monthlyPerformanceBreakDown) return {};\r\n\r\n    return fiscalData.monthlyPerformanceBreakDown.reduce(\r\n      (totals, month) => {\r\n        totals.totalIncome += parseFloat(month.totalIncome || 0);\r\n        totals.totalCOGS += parseFloat(month.totalCOGS || 0);\r\n        totals.totalExpenses += parseFloat(month.totalExpenses || 0);\r\n        return totals;\r\n      },\r\n      { totalIncome: 0, totalCOGS: 0, totalExpenses: 0 }\r\n    );\r\n  };\r\n\r\n  // Function to determine which charts should be rendered and their order\r\n  const getEnabledCharts = () => {\r\n    const allCharts = [\r\n      {\r\n        key: 'incomeSummary',\r\n        title: 'Monthly Performance Breakdown',\r\n        ref: stackedColumnRef,\r\n        options: null, // Will be set in initializeCharts\r\n        hasData: () => {\r\n          if (!fiscalData?.monthlyPerformanceBreakDown) return false;\r\n          const monthlyData = fiscalData.monthlyPerformanceBreakDown;\r\n          return monthlyData.some(item => {\r\n            const income = parseFloat(item.totalIncome || 0);\r\n            const cogs = parseFloat(item.totalCOGS || 0);\r\n            const expenses = parseFloat(item.totalExpenses || 0);\r\n            return income > 0 || cogs > 0 || expenses > 0;\r\n          });\r\n        }\r\n      },\r\n      {\r\n        key: 'netIncome',\r\n        title: 'Net Income/(Loss)',\r\n        ref: netIncomeRef,\r\n        options: null,\r\n        hasData: () => {\r\n          if (!fiscalData?.netIncomeLoss) return false;\r\n          return fiscalData.netIncomeLoss.some(item => parseFloat(item.netIncomeLoss || 0) !== 0);\r\n        }\r\n      },\r\n      {\r\n        key: 'grossProfitMargin',\r\n        title: 'Gross Profit Margin',\r\n        ref: grossProfitRef,\r\n        options: null,\r\n        hasData: () => {\r\n          if (!fiscalData?.monthlyGrossProfitMargin) return false;\r\n          return fiscalData.monthlyGrossProfitMargin.some(item => parseFloat(item.Gross_Profit_Margin || 0) !== 0);\r\n        },\r\n        hasDescription: true,\r\n        description: {\r\n          title: 'Gross Profit Margin',\r\n          content: 'Is a share of Gross Profit in Total Income or the profit left for covering operating and other expenses. A good Gross Profit Margin is high enough to cover overhead and leave a reasonable Net Profit.'\r\n        }\r\n      },\r\n      {\r\n        key: 'netProfitMargin',\r\n        title: 'Net Profit Margin',\r\n        ref: netProfitMarginRef,\r\n        options: null,\r\n        hasData: () => {\r\n          if (!fiscalData?.nerProfitMargin) return false;\r\n          return fiscalData.nerProfitMargin.some(item => parseFloat(item.nerProfitMargin || 0) !== 0);\r\n        },\r\n        hasDescription: true,\r\n        description: {\r\n          title: 'Net Profit Margin',\r\n          content: 'Shows the profit earned per dollar of income. A 10% Net Profit Margin is considered an excellent ratio. If your company has a low Net Profit Margin you are making very little profit after all costs. That implies the revenue is getting eaten up by expenses. It also increases the risk your firm will be unable to meet obligations. With a low margin, a sudden dip in sales over the next month or year could turn your company unprofitable. A high margin indicates your company has solid competitive advantages.'\r\n        }\r\n      }\r\n    ];\r\n\r\n    // Filter charts based on settings and data availability\r\n    return allCharts.filter(chart =>\r\n      shouldDisplayChart(chart.key) && chart.hasData()\r\n    );\r\n  };\r\n\r\n  const ytdTotals = calculateYTDTotals();\r\n  // Fix floating point precision for net profit calculation\r\n  const netProfit =\r\n    Math.round(\r\n      (ytdTotals.totalIncome - ytdTotals.totalCOGS - ytdTotals.totalExpenses) *\r\n      100\r\n    ) / 100;\r\n\r\n  // Get enabled charts for dynamic layout\r\n  const enabledCharts = getEnabledCharts();\r\n\r\n  // Split charts between upper and lower divs\r\n  // Upper div can hold up to 2 charts, lower div gets the rest\r\n  const upperDivCharts = enabledCharts.slice(0, 2);\r\n  const lowerDivCharts = enabledCharts.slice(2);\r\n\r\n  // Helper function to render a chart component\r\n  const renderChart = (chart) => (\r\n    <div key={chart.key} className=\"bg-white p-6 border-b-4 border-blue-900 mb-4\">\r\n      <div\r\n        className=\"text-2xl font-semibold text-teal-600 mb-5\"\r\n        style={subHeadingTextStyle}\r\n      >\r\n        {chart.title}\r\n      </div>\r\n      <div ref={chart.ref}></div>\r\n      {chart.hasDescription && (\r\n        <div className=\"mt-5 text-xl text-gray-600 leading-relaxed rounded-lg pb-5\">\r\n          <div\r\n            className=\"text-teal-600 text-2xl\"\r\n            style={{ ...subHeadingTextStyle, fontWeight: \"lighter\" }}\r\n          >\r\n            {chart.description.title}\r\n          </div>\r\n          <div style={contentTextStyle}>\r\n            {chart.description.content}\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <div className=\"p-5\">\r\n      <div className=\"max-w-6xl h-[420mm] mx-auto bg-white flex flex-col gap-8 p-10 mb-8\">\r\n        {/* Header Section */}\r\n        <div className=\"component-header flex items-center justify-between gap-4  border-b-4 border-blue-900 pb-2\">\r\n          <h1\r\n            className=\"text-4xl font-bold text-gray-800 m-0\"\r\n            style={headerTextStyle}\r\n          >\r\n            Current Fiscal Year\r\n          </h1>\r\n          <p className=\"text-lg text-gray-600 m-0\" style={formatHeaderStyle()}>\r\n            {formatHeaderPeriod(fiscalData?.FYStartYear, fiscalData?.FYStartMonth)} | {formatCompanyName(fiscalData?.companyName)}\r\n          </p>\r\n        </div>\r\n\r\n        {/* YTD Fiscal Metrics Grid */}\r\n        <div className=\"metrics-flex grid grid-cols-4 gap-5 pb-8 border-b-4 border-blue-900\">\r\n          <div className=\"p-4 text-center\">\r\n            <div className=\"text-xl mb-1\"\r\n             style={{...contentTextStyle, fontSize : '20px', color : \"#4b5562\"}}\r\n            >YTD Total Income</div>\r\n            <div className=\"text-4xl font-bold text-gray-600 m-0\">\r\n              {formatNumber(ytdTotals.totalIncome)}\r\n            </div>\r\n          </div>\r\n          <div\r\n            className=\"p-4 text-center\"\r\n            style={{ backgroundColor: \"#d2e9ea\" }}\r\n          >\r\n            <div className=\"text-xl mb-1\"\r\n            style={{...contentTextStyle, fontSize : '20px', color : \"#4b5562\"}}\r\n            >YTD Cost of Goods Sold</div>\r\n            <div className=\"text-4xl font-bold text-gray-600 m-0\">\r\n              {formatNumber(ytdTotals.totalCOGS)}\r\n            </div>\r\n          </div>\r\n          <div className=\"p-4 text-center\">\r\n            <div className=\"text-xl mb-1\"\r\n            style={{...contentTextStyle, fontSize : '20px', color : \"#4b5562\"}}\r\n            >YTD Total Expense</div>\r\n            <div className=\"text-4xl font-bold text-gray-600 m-0\">\r\n              {formatNumber(ytdTotals.totalExpenses)}\r\n            </div>\r\n          </div>\r\n          <div\r\n            className=\"p-4 text-center\"\r\n            style={{ backgroundColor: \"#d2e9ea\" }}\r\n          >\r\n            <div className=\"text-xl mb-1\"\r\n            style={{...contentTextStyle, fontSize : '20px', color : \"#4b5562\"}}\r\n            >YTD Net Profit</div>\r\n            <div className=\"text-4xl font-bold text-gray-600 m-0\">\r\n              {formatNumber(netProfit)}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Dynamically render charts for upper div */}\r\n        {upperDivCharts.map(chart => renderChart(chart))}\r\n\r\n      </div>\r\n\r\n      {/* Only render lower div if there are charts to display */}\r\n      {lowerDivCharts.length > 0 && (\r\n        <div className=\"max-w-6xl h-[420mm] mx-auto bg-white flex flex-col gap-10 p-10\">\r\n          <div className=\"component-header flex items-center justify-between gap-4 mb-8 border-b-4 border-blue-900 pt-2 pb-2\">\r\n            <h1\r\n              className=\"text-4xl font-bold text-gray-800 m-0\"\r\n              style={headerTextStyle}\r\n            >\r\n              Current Fiscal Year\r\n            </h1>\r\n            <p className=\"text-lg text-gray-600 m-0\" style={formatHeaderStyle()}>\r\n              {formatHeaderPeriod(fiscalData?.FYStartYear, fiscalData?.FYStartMonth)} | {formatCompanyName(fiscalData?.companyName)}\r\n            </p>\r\n          </div>\r\n\r\n          {/* Dynamically render charts for lower div */}\r\n          {lowerDivCharts.map(chart => renderChart(chart))}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FiscalYearDashboard;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,OAAOC,UAAU,MAAM,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,mBAAmB,GAAGA,CAAC;EAC3BC,eAAe,GAAG,CAAC,CAAC;EACpBC,gBAAgB,GAAG,CAAC,CAAC;EACrBC,mBAAmB,GAAG,CAAC,CAAC;EACxBC,gBAAgB,GAAG,CAAC,CAAC;EACrBC,UAAU,GAAG,IAAI;EACjBC,eAAe,GAAG,IAAI,CAAE;AAC1B,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,gBAAgB,GAAGZ,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMa,YAAY,GAAGb,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMc,cAAc,GAAGd,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMe,kBAAkB,GAAGf,MAAM,CAAC,IAAI,CAAC;;EAEvC;EACA,MAAMgB,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAACP,UAAU,EAAE;MACf,OAAO,KAAK;IACd;;IAEA;IACA,MAAMQ,cAAc,GAAGR,UAAU,CAACS,2BAA2B,IAC3DC,KAAK,CAACC,OAAO,CAACX,UAAU,CAACS,2BAA2B,CAAC,IACrDT,UAAU,CAACS,2BAA2B,CAACG,MAAM,GAAG,CAAC;IAEnD,MAAMC,oBAAoB,GAAGb,UAAU,CAACc,wBAAwB,IAC9DJ,KAAK,CAACC,OAAO,CAACX,UAAU,CAACc,wBAAwB,CAAC,IAClDd,UAAU,CAACc,wBAAwB,CAACF,MAAM,GAAG,CAAC;IAEhD,MAAMG,kBAAkB,GAAGf,UAAU,CAACgB,eAAe,IACnDN,KAAK,CAACC,OAAO,CAACX,UAAU,CAACgB,eAAe,CAAC,IACzChB,UAAU,CAACgB,eAAe,CAACJ,MAAM,GAAG,CAAC;IAEvC,MAAMK,gBAAgB,GAAGjB,UAAU,CAACkB,aAAa,IAC/CR,KAAK,CAACC,OAAO,CAACX,UAAU,CAACkB,aAAa,CAAC,IACvClB,UAAU,CAACkB,aAAa,CAACN,MAAM,GAAG,CAAC;IACtC;IACC,OAAOJ,cAAc;EACvB,CAAC;;EAED;EACA,MAAMW,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,EAACnB,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAES,2BAA2B,GAAE;MAC5C,OAAO,KAAK;IACd;IAEA,MAAMW,WAAW,GAAGpB,UAAU,CAACS,2BAA2B;;IAE1D;IACA,MAAMY,wBAAwB,GAAGD,WAAW,CAACE,IAAI,CAACC,IAAI,IAAI;MACxD,MAAMC,MAAM,GAAGC,UAAU,CAACF,IAAI,CAACG,WAAW,IAAI,CAAC,CAAC;MAChD,MAAMC,IAAI,GAAGF,UAAU,CAACF,IAAI,CAACK,SAAS,IAAI,CAAC,CAAC;MAC5C,MAAMC,QAAQ,GAAGJ,UAAU,CAACF,IAAI,CAACO,aAAa,IAAI,CAAC,CAAC;MAEpD,OAAON,MAAM,GAAG,CAAC,IAAIG,IAAI,GAAG,CAAC,IAAIE,QAAQ,GAAG,CAAC;IAC/C,CAAC,CAAC;IACF,OAAOR,wBAAwB;EACjC,CAAC;;EAED;EACA,MAAMU,kBAAkB,GAAIC,QAAQ,IAAK;IACvC,IAAI,EAAC/B,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEgC,aAAa,GAAE,OAAO,IAAI,CAAC,CAAC;IAClD,OAAOhC,eAAe,CAACgC,aAAa,CAACD,QAAQ,CAAC,KAAK,IAAI;EACzD,CAAC;EAED1C,SAAS,CAAC,MAAM;IACd,IAAIiB,YAAY,CAAC,CAAC,EAAE;MAClB;MACA,CAACJ,gBAAgB,EAAEC,YAAY,EAAEC,cAAc,EAAEC,kBAAkB,CAAC,CAAC4B,OAAO,CAAEC,GAAG,IAAK;QACpF,IAAIA,GAAG,CAACC,OAAO,EAAE;UACfD,GAAG,CAACC,OAAO,CAACC,SAAS,GAAG,EAAE;QAC5B;MACF,CAAC,CAAC;MACF;MACAC,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAACtC,UAAU,EAAEC,eAAe,CAAC,CAAC,CAAC,CAAC;;EAEnC,MAAMsC,eAAe,GAAGA,CAACC,IAAI,EAAEC,KAAK,KAAK;IACvC,MAAMC,UAAU,GAAG,CACjB,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACN;IACD,OAAO,GAAGA,UAAU,CAACD,KAAK,GAAG,CAAC,CAAC,IAAIE,MAAM,CAACH,IAAI,CAAC,CAACI,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;EAC7D,CAAC;EAED,SAASC,YAAYA,CAACC,GAAG,EAAE;IACzB;IACA,MAAMC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACH,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG;IAC9C,MAAMI,UAAU,GAAGH,UAAU,GAAG,CAAC;IACjC,MAAMI,MAAM,GAAGH,IAAI,CAACI,GAAG,CAACL,UAAU,CAAC;;IAEnC;IACA,IAAII,MAAM,GAAG,IAAI,EAAE;MACjB,OAAO,CAACD,UAAU,GAAG,GAAG,GAAG,EAAE,KAAKC,MAAM,GAAG,CAAC,KAAK,CAAC,GAAGA,MAAM,CAACE,QAAQ,CAAC,CAAC,GAAGF,MAAM,CAACG,OAAO,CAAC,CAAC,CAAC,CAAC;IAC7F;IAEA,MAAMC,QAAQ,GAAG,CACf;MAAEC,KAAK,EAAE,IAAI;MAAEC,MAAM,EAAE;IAAI,CAAC,EAC5B;MAAED,KAAK,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAI,CAAC,EAC3B;MAAED,KAAK,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAI,CAAC,EAC3B;MAAED,KAAK,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAI,CAAC,CAC5B;IAED,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,QAAQ,CAAC3C,MAAM,EAAE8C,CAAC,EAAE,EAAE;MACxC,IAAIP,MAAM,IAAII,QAAQ,CAACG,CAAC,CAAC,CAACF,KAAK,EAAE;QAC/B,MAAMG,SAAS,GAAG,CAACR,MAAM,GAAGI,QAAQ,CAACG,CAAC,CAAC,CAACF,KAAK,EAAEF,OAAO,CAAC,CAAC,CAAC;QACzD,MAAMM,cAAc,GAAGD,SAAS,CAACE,QAAQ,CAAC,IAAI,CAAC,GAC3CF,SAAS,CAACf,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GACtBe,SAAS;QACb,OAAO,CAACT,UAAU,GAAG,GAAG,GAAG,EAAE,IAAIU,cAAc,GAAGL,QAAQ,CAACG,CAAC,CAAC,CAACD,MAAM;MACtE;IACF;IAEA,OAAO,CAACP,UAAU,GAAG,GAAG,GAAG,EAAE,IAAIH,UAAU,CAACM,QAAQ,CAAC,CAAC;EACxD;EAEA,MAAMf,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,EAACtC,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAES,2BAA2B,GAAE;IAE9C,MAAMW,WAAW,GAAGpB,UAAU,CAACS,2BAA2B;;IAE1D;IACA,MAAMqD,UAAU,GAAG1C,WAAW,CAAC2C,GAAG,CAAExC,IAAI,IACtCgB,eAAe,CAAChB,IAAI,CAACiB,IAAI,EAAEjB,IAAI,CAACkB,KAAK,CACvC,CAAC;;IAED;IACA,MAAMuB,kBAAkB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACpC,IAAIjE,UAAU,CAACgB,eAAe,IAAIN,KAAK,CAACC,OAAO,CAACX,UAAU,CAACgB,eAAe,CAAC,EAAE;MAC3EhB,UAAU,CAACgB,eAAe,CAACkB,OAAO,CAACX,IAAI,IAAI;QACzC,MAAM2C,GAAG,GAAG,GAAG3C,IAAI,CAACiB,IAAI,IAAIjB,IAAI,CAACkB,KAAK,EAAE;QACxC,MAAMe,KAAK,GAAG/B,UAAU,CAACF,IAAI,CAACP,eAAe,CAAC,IAAI,CAAC;QACnDgD,kBAAkB,CAACG,GAAG,CAACD,GAAG,EAAEV,KAAK,CAAC;MACpC,CAAC,CAAC;IACJ;IAEA,MAAMY,oBAAoB,GAAG,IAAIH,GAAG,CAAC,CAAC;IACtC,IAAIjE,UAAU,CAACc,wBAAwB,IAAIJ,KAAK,CAACC,OAAO,CAACX,UAAU,CAACc,wBAAwB,CAAC,EAAE;MAC7Fd,UAAU,CAACc,wBAAwB,CAACoB,OAAO,CAACX,IAAI,IAAI;QAClD,MAAM2C,GAAG,GAAG,GAAG3C,IAAI,CAACiB,IAAI,IAAIjB,IAAI,CAACkB,KAAK,EAAE;QACxC,MAAMe,KAAK,GAAG/B,UAAU,CAACF,IAAI,CAAC8C,mBAAmB,CAAC;QAClD,MAAMC,UAAU,GAAGd,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAGA,KAAK,GAAG,GAAG;QAClDY,oBAAoB,CAACD,GAAG,CAACD,GAAG,EAAEI,UAAU,IAAI,CAAC,CAAC;MAChD,CAAC,CAAC;IACJ;IAEA,MAAMC,YAAY,GAAG,IAAIN,GAAG,CAAC,CAAC;IAC9B,IAAIjE,UAAU,CAACkB,aAAa,IAAIR,KAAK,CAACC,OAAO,CAACX,UAAU,CAACkB,aAAa,CAAC,EAAE;MACvElB,UAAU,CAACkB,aAAa,CAACgB,OAAO,CAACX,IAAI,IAAI;QACvC,MAAM2C,GAAG,GAAG,GAAG3C,IAAI,CAACiB,IAAI,IAAIjB,IAAI,CAACkB,KAAK,EAAE;QACxC,MAAMe,KAAK,GAAG/B,UAAU,CAACF,IAAI,CAACL,aAAa,CAAC,GAAG,IAAI,IAAI,CAAC;QACxDqD,YAAY,CAACJ,GAAG,CAACD,GAAG,EAAEV,KAAK,CAAC;MAC9B,CAAC,CAAC;IACJ;;IAEA;IACA,MAAMgB,UAAU,GAAGpD,WAAW,CAAC2C,GAAG,CAAExC,IAAI,IAAKE,UAAU,CAACF,IAAI,CAACG,WAAW,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;IACtF,MAAM+C,QAAQ,GAAGrD,WAAW,CAAC2C,GAAG,CAAExC,IAAI,IAAKE,UAAU,CAACF,IAAI,CAACK,SAAS,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;IAClF,MAAM8C,WAAW,GAAGtD,WAAW,CAAC2C,GAAG,CAAExC,IAAI,IAAKE,UAAU,CAACF,IAAI,CAACO,aAAa,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;IAEzF,MAAM6C,qBAAqB,GAAGvD,WAAW,CAAC2C,GAAG,CAAExC,IAAI,IAAK;MACtD,MAAM2C,GAAG,GAAG,GAAG3C,IAAI,CAACiB,IAAI,IAAIjB,IAAI,CAACkB,KAAK,EAAE;MACxC,OAAO2B,oBAAoB,CAACQ,GAAG,CAACV,GAAG,CAAC,IAAI,CAAC;IAC3C,CAAC,CAAC;IAEFW,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEH,qBAAqB,CAAC;IAEzD,MAAMI,kBAAkB,GAAG3D,WAAW,CAAC2C,GAAG,CAAExC,IAAI,IAAK;MACnD,MAAM2C,GAAG,GAAG,GAAG3C,IAAI,CAACiB,IAAI,IAAIjB,IAAI,CAACkB,KAAK,EAAE;MACxC,OAAOuB,kBAAkB,CAACY,GAAG,CAACV,GAAG,CAAC,IAAI,CAAC;IACzC,CAAC,CAAC;IAEF,MAAMc,aAAa,GAAG5D,WAAW,CAAC2C,GAAG,CAAExC,IAAI,IAAK;MAC9C,MAAM2C,GAAG,GAAG,GAAG3C,IAAI,CAACiB,IAAI,IAAIjB,IAAI,CAACkB,KAAK,EAAE;MACxC,OAAO8B,YAAY,CAACK,GAAG,CAACV,GAAG,CAAC,IAAI,CAAC;IACnC,CAAC,CAAC;;IAEF;IACJ,MAAMe,oBAAoB,GAAG;MAC3BC,MAAM,EAAE,CACN;QAAEC,IAAI,EAAE,QAAQ;QAAEC,IAAI,EAAE,MAAM;QAAEC,IAAI,EAAEb;MAAW,CAAC,EAClD;QAAEW,IAAI,EAAE,SAAS;QAAEC,IAAI,EAAE,QAAQ;QAAEC,IAAI,EAAEX;MAAY,CAAC,EACtD;QAAES,IAAI,EAAE,oBAAoB;QAAEC,IAAI,EAAE,QAAQ;QAAEC,IAAI,EAAEZ;MAAS,CAAC,CAC/D;MACDa,KAAK,EAAE;QACLC,MAAM,EAAE,GAAG;QACXH,IAAI,EAAE,KAAK;QACXI,OAAO,EAAE,IAAI;QACbC,OAAO,EAAE;UAAEC,IAAI,EAAE;QAAM,CAAC;QACxBC,UAAU,EAAE;MACd,CAAC;MACDC,UAAU,EAAE;QACVC,OAAO,EAAE,IAAI;QACbC,eAAe,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAAE;QAC5BC,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAEC,IAAI,EAAE;UAC9B,IAAID,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,IAAIC,KAAK,CAACH,GAAG,CAAC,EAAE,OAAO,EAAE;UAE9D,MAAMI,MAAM,GAAGpD,IAAI,CAACI,GAAG,CAAC4C,GAAG,CAAC;UAE5B,IAAII,MAAM,IAAI,IAAI,EAAE;YAClB,OAAO,GAAG,GAAG,CAACJ,GAAG,GAAG,IAAI,EAAE1C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UAC5C,CAAC,MAAM,IAAI8C,MAAM,IAAI,CAAC,EAAE;YACtB,OAAO,GAAG,GAAGJ,GAAG,CAAC1C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UACnC,CAAC,MAAM;YACL,OAAO,GAAG,GAAG,CAAC0C,GAAG,GAAG,IAAI,EAAE1C,OAAO,CAAC,CAAC,CAAC;UACtC;QACF,CAAC;QACD+C,KAAK,EAAE;UACLC,QAAQ,EAAE,MAAM;UAChBC,MAAM,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC;UAAE;UACrCC,UAAU,EAAE;QACd,CAAC;QACDC,OAAO,EAAE,CAAC,EAAE;QACZd,UAAU,EAAE;UACVE,OAAO,EAAE;QACX,CAAC;QACDa,UAAU,EAAE;UACVb,OAAO,EAAE;QACX;MACF,CAAC;MACDc,MAAM,EAAE;QACNC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAChBC,KAAK,EAAE;MACT,CAAC;MACDC,WAAW,EAAE;QACXC,GAAG,EAAE;UACHC,WAAW,EAAE,KAAK;UAClBpB,UAAU,EAAE;YACVqB,KAAK,EAAE;cACLpB,OAAO,EAAE,KAAK;cAAE;cAChBY,OAAO,EAAE,CAAC,EAAE;cACZJ,KAAK,EAAE;gBACLC,QAAQ,EAAE,MAAM;gBAChBE,UAAU,EAAE,KAAK;gBACjBU,KAAK,EAAE;cACT,CAAC;cACDnB,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;gBACxB,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,IAAIC,KAAK,CAACH,GAAG,CAAC,EAAE,OAAO,IAAI;gBAEhE,MAAMI,MAAM,GAAGpD,IAAI,CAACI,GAAG,CAAC4C,GAAG,CAAC;gBAE5B,IAAII,MAAM,IAAI,IAAI,EAAE;kBAClB,OAAO,GAAG,GAAG,CAACJ,GAAG,GAAG,IAAI,EAAE1C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;gBAC5C,CAAC,MAAM,IAAI8C,MAAM,IAAI,CAAC,EAAE;kBACtB,OAAO,GAAG,GAAGJ,GAAG,CAAC1C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;gBACnC,CAAC,MAAM;kBACL,OAAO,GAAG,GAAG,CAAC0C,GAAG,GAAG,IAAI,EAAE1C,OAAO,CAAC,CAAC,CAAC;gBACtC;cACF;YACF;UACF;QACF;MACF,CAAC;MACD6D,IAAI,EAAE;QACJC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAE;MACtB,CAAC;MACDC,MAAM,EAAEvD,UAAU;MAClBwD,OAAO,EAAE;QACPC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACfjB,QAAQ,EAAE,MAAM;QAChBkB,YAAY,EAAE,MAAM;QACpBC,WAAW,EAAE,CAAC;QACdC,WAAW,EAAE,CAAC;QACdC,KAAK,EAAE;UACLJ,IAAI,EAAE;QACR;MACF,CAAC;MACDK,KAAK,EAAE;QACLP,MAAM,EAAE;UACNhB,KAAK,EAAE;YACLE,MAAM,EAAE,MAAM;YACdD,QAAQ,EAAE;UACZ,CAAC;UACDG,OAAO,EAAE,EAAE,CAAE;QACf,CAAC;QACDoB,UAAU,EAAE;UACVnC,IAAI,EAAE;QACR,CAAC;QACDoC,SAAS,EAAE;UACTpC,IAAI,EAAE;QACR;MACF,CAAC;MACDqC,KAAK,EAAE;QACLrC,IAAI,EAAE,KAAK;QACX;QACAsC,GAAG,EAAE,CAAC;QACNC,GAAG,EAAE,SAAAA,CAAA,EAAY;UACf;UACA,MAAMC,UAAU,GAAGlF,IAAI,CAACiF,GAAG,CAAC,GAAGxD,QAAQ,CAACV,GAAG,CAAC,CAACpC,IAAI,EAAE+B,CAAC,KAAK/B,IAAI,GAAG+C,WAAW,CAAChB,CAAC,CAAC,CAAC,CAAC;UAChF,MAAMyE,SAAS,GAAGnF,IAAI,CAACiF,GAAG,CAAC,GAAGzD,UAAU,CAAC;UACzC,OAAOxB,IAAI,CAACiF,GAAG,CAACC,UAAU,EAAEC,SAAS,CAAC,GAAG,GAAG;QAC9C;MACF,CAAC;MACD5B,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MAAE;MAC3C6B,MAAM,EAAE;QACNC,QAAQ,EAAE,QAAQ;QAClBC,eAAe,EAAE,QAAQ;QACzBhC,QAAQ,EAAE,MAAM;QAChBE,UAAU,EAAE,KAAK;QACjBc,OAAO,EAAE;UACPV,KAAK,EAAE,EAAE;UACTrB,MAAM,EAAE,EAAE;UACVgD,MAAM,EAAE,CAAC,CAAE;QACb,CAAC;QACDlB,MAAM,EAAE;UACNd,MAAM,EAAE,MAAM;UACdiC,eAAe,EAAE;QACnB,CAAC;QACDC,UAAU,EAAE;UACVC,UAAU,EAAE,EAAE;UACdC,QAAQ,EAAE;QACZ,CAAC;QACDlC,OAAO,EAAE,EAAE;QACXmC,WAAW,EAAE;UACXC,gBAAgB,EAAE,IAAI,CAAE;QAC1B,CAAC;QACDC,WAAW,EAAE;UACXC,mBAAmB,EAAE,IAAI,CAAE;QAC7B;MACF,CAAC;MACDC,OAAO,EAAE;QACPC,MAAM,EAAE,IAAI;QACZC,SAAS,EAAE,KAAK;QAChB;QACAC,MAAM,EAAE,SAAAA,CAAU;UAAEjE,MAAM;UAAEkE,WAAW;UAAEC,cAAc;UAAEC;QAAE,CAAC,EAAE;UAC5D,MAAM9H,MAAM,GAAG,CAAC0D,MAAM,CAAC,CAAC,CAAC,CAACmE,cAAc,CAAC,GAAG,IAAI,EAAE/F,OAAO,CAAC,CAAC,CAAC;UAC5D,MAAMiG,OAAO,GAAG,CAACrE,MAAM,CAAC,CAAC,CAAC,CAACmE,cAAc,CAAC,GAAG,IAAI,EAAE/F,OAAO,CAAC,CAAC,CAAC;UAC7D,MAAM3B,IAAI,GAAG,CAACuD,MAAM,CAAC,CAAC,CAAC,CAACmE,cAAc,CAAC,GAAG,IAAI,EAAE/F,OAAO,CAAC,CAAC,CAAC;UAC1D,MAAMkG,QAAQ,GAAGF,CAAC,CAACG,OAAO,CAACpC,MAAM,CAACgC,cAAc,CAAC;UAEjD,OAAO;AACb;AACA,4EAA4EG,QAAQ;AACpF;AACA;AACA,0DAA0DhI,MAAM;AAChE;AACA;AACA;AACA,2DAA2D+H,OAAO;AAClE;AACA,gFAAgF5H,IAAI,IAAI,CAAC,GAAG,eAAe,GAAG,EAAE;AAChH;AACA,wDAAwDA,IAAI,aAAaA,IAAI,IAAI,CAAC,GAAG,WAAW,GAAG,EAAE;AACrG;AACA;AACA,oCAAoC,CAACF,UAAU,CAAC8H,OAAO,CAAC,GAAG9H,UAAU,CAACE,IAAI,CAAC,EAAE2B,OAAO,CAAC,CAAC,CAAC;AACvF;AACA;AACA,OAAO;QACH;MACF,CAAC;MACDoG,IAAI,EAAE;QACJhE,IAAI,EAAE,KAAK;QACXiE,OAAO,EAAE;UACPC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAE,EAAE;UACTC,GAAG,EAAE,EAAE;UACPC,MAAM,EAAE,EAAE,CAAE;QACd;MACF,CAAC;MACD;MACAC,WAAW,EAAE;QACXjC,KAAK,EAAEtD,QAAQ,CAACnD,IAAI,CAAC0E,GAAG,IAAIA,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;UAC3CiE,CAAC,EAAE,CAAC;UACJC,WAAW,EAAE,SAAS;UACtBC,KAAK,EAAE;YACLD,WAAW,EAAE,SAAS;YACtB7D,KAAK,EAAE;cACLa,KAAK,EAAE,MAAM;cACbvB,UAAU,EAAE;YACd;UACF;QACF,CAAC;MACH;IACF,CAAC;;IAEG;IACA,MAAMyE,gBAAgB,GAAG;MACvBlF,MAAM,EAAE,CAAC;QACPC,IAAI,EAAE,YAAY;QAClBE,IAAI,EAAEL;MACR,CAAC,CAAC;MACFM,KAAK,EAAE;QACLF,IAAI,EAAE,MAAM;QACZG,MAAM,EAAE,GAAG;QACXE,OAAO,EAAE;UAAEC,IAAI,EAAE;QAAM,CAAC;QACxBC,UAAU,EAAE,aAAa;QACzB0E,IAAI,EAAG;UACLxE,OAAO,EAAG;QACZ;MACF,CAAC;MACDD,UAAU,EAAE;QACVC,OAAO,EAAE,IAAI;QACbE,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;UACxB,IAAIA,GAAG,IAAI,CAAC,EAAE;YACZ,OAAO,GAAG,GAAGA,GAAG,CAAC1C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UACnC,CAAC,MAAM,IAAI0C,GAAG,IAAI,GAAG,EAAE;YACrB,OAAO,GAAG,GAAGA,GAAG,CAAC1C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UACnC,CAAC,MAAM,IAAI0C,GAAG,IAAI,CAAC,GAAG,EAAE;YACtB,OAAO,IAAI,GAAGhD,IAAI,CAACI,GAAG,CAAC4C,GAAG,CAAC,CAAC1C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UAC9C,CAAC,MAAM;YACL,OAAO,GAAG,GAAG0C,GAAG,CAAC1C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UACnC;QACF,CAAC;QACD+C,KAAK,EAAE;UACLC,QAAQ,EAAE,MAAM;UAChBC,MAAM,EAAE,CAAC,MAAM,CAAC;UAChBC,UAAU,EAAE;QACd,CAAC;QACDC,OAAO,EAAE,CAAC,EAAE;QACZd,UAAU,EAAE;UACVE,OAAO,EAAE;QACX,CAAC;QACDa,UAAU,EAAE;UACVb,OAAO,EAAE;QACX;MACF,CAAC;MACDc,MAAM,EAAE;QACNE,KAAK,EAAE,UAAU;QACjBD,KAAK,EAAE;MACT,CAAC;MACDO,IAAI,EAAE;QACJ/B,IAAI,EAAE;MACR,CAAC;MACDkC,OAAO,EAAE;QACPC,IAAI,EAAE,CAAC;QACPC,YAAY,EAAE,MAAM;QACpBC,WAAW,EAAE,CAAC;QACdlB,MAAM,EAAEvB,aAAa,CAACjB,GAAG,CAACiC,GAAG,IAAIA,GAAG,IAAI,CAAC,GAAG,SAAS,GAAG,SAAS,CAAC;QAClE2B,KAAK,EAAE;UACLJ,IAAI,EAAE;QACR,CAAC;QACD+C,QAAQ,EAAEtF,aAAa,CAACjB,GAAG,CAAC,CAACiC,GAAG,EAAEuE,KAAK,MAAM;UAC3CnB,WAAW,EAAE,CAAC;UACdC,cAAc,EAAEkB,KAAK;UACrBC,SAAS,EAAExE,GAAG,IAAI,CAAC,GAAG,SAAS,GAAG,SAAS;UAC3CyE,WAAW,EAAE,MAAM;UACnBlD,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC;MACDK,KAAK,EAAE;QACL9D,UAAU,EAAEA,UAAU;QACtBuD,MAAM,EAAE;UACNhB,KAAK,EAAE;YACLE,MAAM,EAAE,MAAM;YACdD,QAAQ,EAAE;UACZ;QACF,CAAC;QACDuB,UAAU,EAAE;UAAEnC,IAAI,EAAE;QAAM,CAAC;QAC3BoC,SAAS,EAAE;UAAEpC,IAAI,EAAE;QAAM;MAC3B,CAAC;MACDqC,KAAK,EAAE;QACLrC,IAAI,EAAE;MACR,CAAC;MACDa,MAAM,EAAE,CAAC,SAAS,CAAC;MAAE;MACrBO,WAAW,EAAE;QACX4D,IAAI,EAAE;UACJnE,MAAM,EAAE;YACNoE,SAAS,EAAE,CAAC;YACZC,mBAAmB,EAAE,SAAS;YAAE;YAChCC,mBAAmB,EAAE,SAAS,CAAG;UACnC;QACF;MACF,CAAC;MACD7B,OAAO,EAAE;QACPiB,CAAC,EAAE;UACDlE,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;YACxB,IAAIA,GAAG,IAAI,CAAC,EAAE;cACZ,OAAO,GAAG,GAAGA,GAAG,CAAC1C,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI;YACpC,CAAC,MAAM,IAAI0C,GAAG,IAAI,GAAG,EAAE;cACrB,OAAO,GAAG,GAAGA,GAAG,CAAC1C,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI;YACpC,CAAC,MAAM,IAAI0C,GAAG,IAAI,CAAC,GAAG,EAAE;cACtB,OAAO,IAAI,GAAGhD,IAAI,CAACI,GAAG,CAAC4C,GAAG,CAAC,CAAC1C,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI;YAC/C,CAAC,MAAM;cACL,OAAO,GAAG,GAAG0C,GAAG,CAAC1C,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI;YACpC;UACF;QACF;MACF,CAAC;MACDoG,IAAI,EAAE;QACJhE,IAAI,EAAE,KAAK;QACXiE,OAAO,EAAE;UACPC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAE,EAAE;UACTC,GAAG,EAAE,EAAE;UACPC,MAAM,EAAE;QACV;MACF,CAAC;MACDC,WAAW,EAAE;QACXjC,KAAK,EAAE,CAAC;UACNkC,CAAC,EAAE,CAAC;UACJC,WAAW,EAAE,MAAM;UACnBY,WAAW,EAAE,CAAC;UACdC,eAAe,EAAE,CAAC;UAClB3D,OAAO,EAAE;QACX,CAAC;MACH;IACF,CAAC;;IAED;IACA,MAAM4D,kBAAkB,GAAG;MACzB9F,MAAM,EAAE,CAAC;QAAEC,IAAI,EAAE,qBAAqB;QAAEE,IAAI,EAAEV;MAAsB,CAAC,CAAC;MACtEW,KAAK,EAAE;QACLF,IAAI,EAAE,KAAK;QACXG,MAAM,EAAE,GAAG;QACXE,OAAO,EAAE;UAAEC,IAAI,EAAE;QAAM,CAAC;QACxBC,UAAU,EAAE;MACd,CAAC;MACDmB,WAAW,EAAE;QACXC,GAAG,EAAE;UACH2B,UAAU,EAAE,KAAK;UACjB1B,WAAW,EAAE,KAAK;UAClBiE,WAAW,EAAE,SAAS;UACtBrF,UAAU,EAAE;YAAEyC,QAAQ,EAAE;UAAM;QAChC;MACF,CAAC;MACDzC,UAAU,EAAE;QACVC,OAAO,EAAE,IAAI;QACbwC,QAAQ,EAAE,KAAK;QAChBtC,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;UACvB,IAAIA,GAAG,IAAI,CAAC,EAAE;YACZ,OAAOA,GAAG,CAAC1C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UAC7B,CAAC,MAAM,IAAI0C,GAAG,IAAI,GAAG,EAAE;YACrB,OAAOA,GAAG,CAAC1C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UAC7B,CAAC,MAAM,IAAI0C,GAAG,IAAI,CAAC,GAAG,EAAE;YACtB,OAAO,GAAG,GAAGhD,IAAI,CAACI,GAAG,CAAC4C,GAAG,CAAC,CAAC1C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UAC7C,CAAC,MAAM;YACL,OAAO0C,GAAG,CAAC1C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UAC7B;QACF,CAAC;QACDmD,OAAO,EAAE,CAAC,EAAE;QACZJ,KAAK,EAAE;UAAEC,QAAQ,EAAE,MAAM;UAAEC,MAAM,EAAE,CAAC,MAAM,CAAC;UAAEC,UAAU,EAAE;QAAM;MACjE,CAAC;MACDG,MAAM,EAAE;QAAEjB,IAAI,EAAE,IAAI;QAAEkB,KAAK,EAAE,CAAC;QAAEL,MAAM,EAAE,CAAC,aAAa;MAAE,CAAC;MACzDqB,KAAK,EAAE;QACL9D,UAAU,EAAEA,UAAU;QACtBuD,MAAM,EAAE;UAAEhB,KAAK,EAAE;YAAEE,MAAM,EAAE,MAAM;YAAED,QAAQ,EAAE;UAAO;QAAE,CAAC;QACvDuB,UAAU,EAAE;UAAEnC,IAAI,EAAE;QAAM,CAAC;QAC3BoC,SAAS,EAAE;UAAEpC,IAAI,EAAE;QAAM;MAC3B,CAAC;MACDqC,KAAK,EAAE;QACLrC,IAAI,EAAE,KAAK;QACXsC,GAAG,EAAEhF,IAAI,CAACgF,GAAG,CAAC,GAAGrD,qBAAqB,CAAC,GAAG,CAAC,GAAG3B,IAAI,CAACgF,GAAG,CAAC,GAAGrD,qBAAqB,CAAC,GAAG,GAAG,GAAG,CAAC;QAC1FsD,GAAG,EAAEjF,IAAI,CAACiF,GAAG,CAAC,GAAGtD,qBAAqB,CAAC,GAAG;MAC5C,CAAC;MACDwC,IAAI,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAC;MACpBb,MAAM,EAAE,CAAC,SAAS,CAAC;MACnByC,OAAO,EAAE;QACPiB,CAAC,EAAE;UACDlE,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;YACxB,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,IAAIC,KAAK,CAACH,GAAG,CAAC,EAAE,OAAO,KAAK;YACjE,OAAOA,GAAG,CAAC1C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UAC7B;QACF;MACF,CAAC;MACDoG,IAAI,EAAE;QACJhE,IAAI,EAAE,KAAK;QACXiE,OAAO,EAAE;UAAEC,IAAI,EAAE,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAEC,GAAG,EAAE,EAAE;UAAEC,MAAM,EAAE;QAAE;MACrD;IACF,CAAC;;IAED;IACA,MAAMmB,sBAAsB,GAAG;MAC7BhG,MAAM,EAAE,CAAC;QACPC,IAAI,EAAE,mBAAmB;QACzBE,IAAI,EAAEN;MACR,CAAC,CAAC;MACFO,KAAK,EAAE;QACLF,IAAI,EAAE,MAAM;QACZG,MAAM,EAAE,GAAG;QACXE,OAAO,EAAE;UAAEC,IAAI,EAAE;QAAM,CAAC;QACxBC,UAAU,EAAE,aAAa;QACzB0E,IAAI,EAAG;UACLxE,OAAO,EAAG;QACZ;MACF,CAAC;MACDD,UAAU,EAAE;QACVC,OAAO,EAAE,IAAI;QACbE,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;UACxB,IAAIA,GAAG,IAAI,CAAC,EAAE;YACZ,OAAOA,GAAG,CAAC1C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UAC7B,CAAC,MAAM,IAAI0C,GAAG,IAAI,GAAG,EAAE;YACrB,OAAOA,GAAG,CAAC1C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UAC7B,CAAC,MAAM,IAAI0C,GAAG,IAAI,CAAC,GAAG,EAAE;YACtB,OAAO,GAAG,GAAGhD,IAAI,CAACI,GAAG,CAAC4C,GAAG,CAAC,CAAC1C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UAC7C,CAAC,MAAM;YACL,OAAO0C,GAAG,CAAC1C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UAC7B;QACF,CAAC;QACD+C,KAAK,EAAE;UACLC,QAAQ,EAAE,MAAM;UAChBC,MAAM,EAAE,CAAC,MAAM,CAAC;UAChBC,UAAU,EAAE;QACd,CAAC;QACDC,OAAO,EAAE,CAAC,EAAE;QACZd,UAAU,EAAE;UACVE,OAAO,EAAE;QACX,CAAC;QACDa,UAAU,EAAE;UACVb,OAAO,EAAE;QACX;MACF,CAAC;MACDc,MAAM,EAAE;QACNE,KAAK,EAAE,UAAU;QACjBD,KAAK,EAAE;MACT,CAAC;MACDO,IAAI,EAAE;QACJ/B,IAAI,EAAE;MACR,CAAC;MACDkC,OAAO,EAAE;QACPC,IAAI,EAAE,CAAC;QACPC,YAAY,EAAE,MAAM;QACpBC,WAAW,EAAE,CAAC;QACdlB,MAAM,EAAExB,kBAAkB,CAAChB,GAAG,CAACiC,GAAG,IAAIA,GAAG,IAAI,CAAC,GAAG,SAAS,GAAG,SAAS,CAAC;QACvE2B,KAAK,EAAE;UACLJ,IAAI,EAAE;QACR,CAAC;QACD+C,QAAQ,EAAEvF,kBAAkB,CAAChB,GAAG,CAAC,CAACiC,GAAG,EAAEuE,KAAK,MAAM;UAChDnB,WAAW,EAAE,CAAC;UACdC,cAAc,EAAEkB,KAAK;UACrBC,SAAS,EAAExE,GAAG,IAAI,CAAC,GAAG,SAAS,GAAG,SAAS;UAC3CyE,WAAW,EAAE,MAAM;UACnBlD,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC;MACDK,KAAK,EAAE;QACL9D,UAAU,EAAEA,UAAU;QACtBuD,MAAM,EAAE;UACNhB,KAAK,EAAE;YACLE,MAAM,EAAE,MAAM;YACdD,QAAQ,EAAE;UACZ;QACF,CAAC;QACDuB,UAAU,EAAE;UAAEnC,IAAI,EAAE;QAAM,CAAC;QAC3BoC,SAAS,EAAE;UAAEpC,IAAI,EAAE;QAAM;MAC3B,CAAC;MACDqC,KAAK,EAAE;QACLrC,IAAI,EAAE;MACR,CAAC;MACDa,MAAM,EAAE,CAAC,SAAS,CAAC;MAAE;MACrBO,WAAW,EAAE;QACX4D,IAAI,EAAE;UACJnE,MAAM,EAAE;YACNoE,SAAS,EAAE,CAAC;YACZC,mBAAmB,EAAE,SAAS;YAC9BC,mBAAmB,EAAE;UACvB;QACF;MACF,CAAC;MACD7B,OAAO,EAAE;QACPiB,CAAC,EAAE;UACDlE,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;YACxB,IAAIA,GAAG,IAAI,CAAC,EAAE;cACZ,OAAOA,GAAG,CAAC1C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;YAC7B,CAAC,MAAM,IAAI0C,GAAG,IAAI,GAAG,EAAE;cACrB,OAAOA,GAAG,CAAC1C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;YAC7B,CAAC,MAAM,IAAI0C,GAAG,IAAI,CAAC,GAAG,EAAE;cACtB,OAAO,GAAG,GAAGhD,IAAI,CAACI,GAAG,CAAC4C,GAAG,CAAC,CAAC1C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;YAC7C,CAAC,MAAM;cACL,OAAO0C,GAAG,CAAC1C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;YAC7B;UACF;QACF;MACF,CAAC;MACDoG,IAAI,EAAE;QACJhE,IAAI,EAAE,KAAK;QACXiE,OAAO,EAAE;UACPC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAE,EAAE;UACTC,GAAG,EAAE,EAAE;UACPC,MAAM,EAAE;QACV;MACF,CAAC;MACDC,WAAW,EAAE;QACXjC,KAAK,EAAE,CAAC;UACNkC,CAAC,EAAE,CAAC;UACJC,WAAW,EAAE,MAAM;UACnBY,WAAW,EAAE,CAAC;UACdC,eAAe,EAAE,CAAC;UAClB3D,OAAO,EAAE;QACX,CAAC;MACH;IACF,CAAC;;IAED;IACA,CAACjH,gBAAgB,EAAEC,YAAY,EAAEC,cAAc,EAAEC,kBAAkB,CAAC,CAAC4B,OAAO,CAAEC,GAAG,IAAK;MACpF,IAAIA,GAAG,CAACC,OAAO,EAAE;QACfD,GAAG,CAACC,OAAO,CAACC,SAAS,GAAG,EAAE;MAC5B;IACF,CAAC,CAAC;;IAIF;IACA,MAAM8I,aAAa,GAAGC,gBAAgB,CAAC,CAAC;;IAExC;IACAD,aAAa,CAACjJ,OAAO,CAACoD,KAAK,IAAI;MAC7B,QAAQA,KAAK,CAACpB,GAAG;QACf,KAAK,eAAe;UAClBoB,KAAK,CAAC+F,OAAO,GAAGpG,oBAAoB;UACpCK,KAAK,CAACH,IAAI,GAAG,gBAAgB;UAC7B;QACF,KAAK,WAAW;UACdG,KAAK,CAAC+F,OAAO,GAAGjB,gBAAgB;UAChC9E,KAAK,CAACH,IAAI,GAAG,YAAY;UACzB;QACF,KAAK,mBAAmB;UACtBG,KAAK,CAAC+F,OAAO,GAAGL,kBAAkB;UAClC1F,KAAK,CAACH,IAAI,GAAG,qBAAqB;UAClC;QACF,KAAK,iBAAiB;UACpBG,KAAK,CAAC+F,OAAO,GAAGH,sBAAsB;UACtC5F,KAAK,CAACH,IAAI,GAAG,mBAAmB;UAChC;MACJ;IACF,CAAC,CAAC;;IAEF;IACA,MAAMmG,mBAAmB,GAAGA,CAACnJ,GAAG,EAAEkJ,OAAO,EAAEE,SAAS,KAAK;MACvD,IAAIpJ,GAAG,CAACC,OAAO,EAAE;QACf;QACAD,GAAG,CAACC,OAAO,CAACC,SAAS,GAAG,EAAE;;QAE1B;QACAmJ,UAAU,CAAC,MAAM;UACf,IAAIrJ,GAAG,CAACC,OAAO,EAAE;YACf,IAAI;cACF,MAAMkD,KAAK,GAAG,IAAI9F,UAAU,CAAC2C,GAAG,CAACC,OAAO,EAAEiJ,OAAO,CAAC;cAClD/F,KAAK,CAACmG,MAAM,CAAC,CAAC;;cAEd;cACA,IAAIF,SAAS,KAAK,gBAAgB,EAAE;gBAClCG,MAAM,CAACC,kBAAkB,GAAGrG,KAAK;cACnC,CAAC,MAAM,IAAIiG,SAAS,KAAK,YAAY,EAAE;gBACrCG,MAAM,CAACE,cAAc,GAAGtG,KAAK;cAC/B,CAAC,MAAM,IAAIiG,SAAS,KAAK,qBAAqB,EAAE;gBAC9CG,MAAM,CAACG,gBAAgB,GAAGvG,KAAK;cACjC,CAAC,MAAM,IAAIiG,SAAS,KAAK,mBAAmB,EAAE;gBAC5CG,MAAM,CAACI,oBAAoB,GAAGxG,KAAK;cACrC;YACF,CAAC,CAAC,OAAOyG,KAAK,EAAE;cACdlH,OAAO,CAACkH,KAAK,CAAC,gCAAgCR,SAAS,SAAS,EAAEQ,KAAK,CAAC;cACxE;cACA5J,GAAG,CAACC,OAAO,CAACC,SAAS,GAAG,kFAAkFkJ,SAAS,cAAc;YACnI;UACF;QACF,CAAC,EAAE,EAAE,CAAC;MACR;IACF,CAAC;;IAED;IACA,MAAMS,iBAAiB,GAAIC,SAAS,IAAK;MACvC,OAAOA,SAAS,IAAIA,SAAS,CAACrL,MAAM,GAAG,CAAC,IAAIqL,SAAS,CAAC3K,IAAI,CAAC0E,GAAG,IAAIvE,UAAU,CAACuE,GAAG,CAAC,KAAK,CAAC,CAAC;IAC1F,CAAC;;IAED;IACAmF,aAAa,CAACjJ,OAAO,CAAC,CAAC;MAAEC,GAAG;MAAEkJ,OAAO;MAAElG,IAAI;MAAEjB;IAAI,CAAC,KAAK;MACrD,IAAI/B,GAAG,CAACC,OAAO,EAAE;QACf,IAAI8J,OAAO,GAAG,KAAK;;QAEnB;QACA,IAAIhI,GAAG,KAAK,eAAe,EAAE;UAC3B,MAAM9C,WAAW,GAAG,CAAApB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAES,2BAA2B,KAAI,EAAE;UACjEyL,OAAO,GAAG9K,WAAW,CAACE,IAAI,CAACC,IAAI,IAAI;YACjC,MAAMC,MAAM,GAAGC,UAAU,CAACF,IAAI,CAACG,WAAW,IAAI,CAAC,CAAC;YAChD,MAAMC,IAAI,GAAGF,UAAU,CAACF,IAAI,CAACK,SAAS,IAAI,CAAC,CAAC;YAC5C,MAAMC,QAAQ,GAAGJ,UAAU,CAACF,IAAI,CAACO,aAAa,IAAI,CAAC,CAAC;YACpD,OAAON,MAAM,KAAK,CAAC,IAAIG,IAAI,KAAK,CAAC,IAAIE,QAAQ,KAAK,CAAC;UACrD,CAAC,CAAC;QACJ,CAAC,MAAM,IAAIqC,GAAG,KAAK,WAAW,EAAE;UAC9B,MAAM9C,WAAW,GAAG,CAAApB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAES,2BAA2B,KAAI,EAAE;UACjE,MAAMuE,aAAa,GAAG5D,WAAW,CAAC2C,GAAG,CAACxC,IAAI,IAAIE,UAAU,CAACF,IAAI,CAAC4K,SAAS,IAAI,CAAC,CAAC,CAAC;UAC9ED,OAAO,GAAGF,iBAAiB,CAAChH,aAAa,CAAC;QAC5C,CAAC,MAAM,IAAId,GAAG,KAAK,mBAAmB,EAAE;UAAA,IAAAkI,qBAAA;UACtC,MAAMC,eAAe,GAAG,CAAArM,UAAU,aAAVA,UAAU,wBAAAoM,qBAAA,GAAVpM,UAAU,CAAEsM,iBAAiB,cAAAF,qBAAA,uBAA7BA,qBAAA,CAA+BrI,GAAG,CAACxC,IAAI,IAAIE,UAAU,CAACF,IAAI,CAAC+K,iBAAiB,IAAI,CAAC,CAAC,CAAC,KAAI,EAAE;UACjHJ,OAAO,GAAGF,iBAAiB,CAACK,eAAe,CAAC;QAC9C,CAAC,MAAM,IAAInI,GAAG,KAAK,iBAAiB,EAAE;UAAA,IAAAqI,qBAAA;UACpC,MAAMC,aAAa,GAAG,CAAAxM,UAAU,aAAVA,UAAU,wBAAAuM,qBAAA,GAAVvM,UAAU,CAAEgB,eAAe,cAAAuL,qBAAA,uBAA3BA,qBAAA,CAA6BxI,GAAG,CAACxC,IAAI,IAAIE,UAAU,CAACF,IAAI,CAACP,eAAe,IAAI,CAAC,CAAC,CAAC,KAAI,EAAE;UAC3GkL,OAAO,GAAGF,iBAAiB,CAACQ,aAAa,CAAC;QAC5C;QAEA,IAAIN,OAAO,EAAE;UACXZ,mBAAmB,CAACnJ,GAAG,EAAEkJ,OAAO,EAAElG,IAAI,CAAC;QACzC,CAAC,MAAM;UACLhD,GAAG,CAACC,OAAO,CAACC,SAAS,GAAG,kFAAkF8C,IAAI,CAACsH,WAAW,CAAC,CAAC,uBAAuB;QACrJ;MACF;IACF,CAAC,CAAC;;IAEF;IACA,CAACtM,gBAAgB,EAAEC,YAAY,EAAEC,cAAc,EAAEC,kBAAkB,CAAC,CAAC4B,OAAO,CAAEC,GAAG,IAAK;MACpF,IAAIA,GAAG,CAACC,OAAO,IAAI,CAAC+I,aAAa,CAAC7J,IAAI,CAACgE,KAAK,IAAIA,KAAK,CAACnD,GAAG,KAAKA,GAAG,CAAC,EAAE;QAClEA,GAAG,CAACC,OAAO,CAACC,SAAS,GAAG,EAAE;MAC5B;IACF,CAAC,CAAC;EACJ,CAAC;EAGD,MAAMqK,kBAAkB,GAAGA,CAACC,SAAS,EAAEC,UAAU,KAAK;IACpD,MAAMlK,UAAU,GAAG,CACjB,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EACtD,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CACjE;IAED,IAAI,CAACiK,SAAS,IAAI,CAACC,UAAU,EAAE;MAC7B,OAAO,GAAG,CAAC,CAAC;IACd;IAEA,MAAMC,cAAc,GAAGnK,UAAU,CAACkK,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;;IAEnD,OAAO,GAAGC,cAAc,IAAIF,SAAS,EAAE;EACzC,CAAC;EAED,MAAMG,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMzG,KAAK,GAAG;MAAE,GAAGzG;IAAgB,CAAC;IAEpC,IAAIyG,KAAK,CAACC,QAAQ,EAAE;MAClB,MAAMA,QAAQ,GAAGyG,QAAQ,CAAC1G,KAAK,CAACC,QAAQ,CAAC;MACzCD,KAAK,CAACC,QAAQ,GAAG,GAAGA,QAAQ,GAAG,CAAC,IAAI;IACtC;IACA,OAAOD,KAAK;EACd,CAAC;EAED,MAAM2G,iBAAiB,GAAIC,WAAW,IAAK;IACzC,IAAI,CAACA,WAAW,EAAE,OAAO,EAAE;IAE3B,IAAIA,WAAW,CAACrM,MAAM,GAAG,EAAE,EAAE;MAC3B,OAAOqM,WAAW,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;IAC7C;IAEA,OAAOD,WAAW;EACpB,CAAC;;EAED;EACA,MAAME,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI,CAACnN,UAAU,CAACS,2BAA2B,EAAE,OAAO,CAAC,CAAC;IAEtD,OAAOT,UAAU,CAACS,2BAA2B,CAAC2M,MAAM,CAClD,CAACC,MAAM,EAAE5K,KAAK,KAAK;MACjB4K,MAAM,CAAC3L,WAAW,IAAID,UAAU,CAACgB,KAAK,CAACf,WAAW,IAAI,CAAC,CAAC;MACxD2L,MAAM,CAACzL,SAAS,IAAIH,UAAU,CAACgB,KAAK,CAACb,SAAS,IAAI,CAAC,CAAC;MACpDyL,MAAM,CAACvL,aAAa,IAAIL,UAAU,CAACgB,KAAK,CAACX,aAAa,IAAI,CAAC,CAAC;MAC5D,OAAOuL,MAAM;IACf,CAAC,EACD;MAAE3L,WAAW,EAAE,CAAC;MAAEE,SAAS,EAAE,CAAC;MAAEE,aAAa,EAAE;IAAE,CACnD,CAAC;EACH,CAAC;;EAED;EACA,MAAMsJ,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMkC,SAAS,GAAG,CAChB;MACEpJ,GAAG,EAAE,eAAe;MACpBqJ,KAAK,EAAE,+BAA+B;MACtCpL,GAAG,EAAEhC,gBAAgB;MACrBkL,OAAO,EAAE,IAAI;MAAE;MACfa,OAAO,EAAEA,CAAA,KAAM;QACb,IAAI,EAAClM,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAES,2BAA2B,GAAE,OAAO,KAAK;QAC1D,MAAMW,WAAW,GAAGpB,UAAU,CAACS,2BAA2B;QAC1D,OAAOW,WAAW,CAACE,IAAI,CAACC,IAAI,IAAI;UAC9B,MAAMC,MAAM,GAAGC,UAAU,CAACF,IAAI,CAACG,WAAW,IAAI,CAAC,CAAC;UAChD,MAAMC,IAAI,GAAGF,UAAU,CAACF,IAAI,CAACK,SAAS,IAAI,CAAC,CAAC;UAC5C,MAAMC,QAAQ,GAAGJ,UAAU,CAACF,IAAI,CAACO,aAAa,IAAI,CAAC,CAAC;UACpD,OAAON,MAAM,GAAG,CAAC,IAAIG,IAAI,GAAG,CAAC,IAAIE,QAAQ,GAAG,CAAC;QAC/C,CAAC,CAAC;MACJ;IACF,CAAC,EACD;MACEqC,GAAG,EAAE,WAAW;MAChBqJ,KAAK,EAAE,mBAAmB;MAC1BpL,GAAG,EAAE/B,YAAY;MACjBiL,OAAO,EAAE,IAAI;MACba,OAAO,EAAEA,CAAA,KAAM;QACb,IAAI,EAAClM,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEkB,aAAa,GAAE,OAAO,KAAK;QAC5C,OAAOlB,UAAU,CAACkB,aAAa,CAACI,IAAI,CAACC,IAAI,IAAIE,UAAU,CAACF,IAAI,CAACL,aAAa,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC;MACzF;IACF,CAAC,EACD;MACEgD,GAAG,EAAE,mBAAmB;MACxBqJ,KAAK,EAAE,qBAAqB;MAC5BpL,GAAG,EAAE9B,cAAc;MACnBgL,OAAO,EAAE,IAAI;MACba,OAAO,EAAEA,CAAA,KAAM;QACb,IAAI,EAAClM,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEc,wBAAwB,GAAE,OAAO,KAAK;QACvD,OAAOd,UAAU,CAACc,wBAAwB,CAACQ,IAAI,CAACC,IAAI,IAAIE,UAAU,CAACF,IAAI,CAAC8C,mBAAmB,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC;MAC1G,CAAC;MACDmJ,cAAc,EAAE,IAAI;MACpBC,WAAW,EAAE;QACXF,KAAK,EAAE,qBAAqB;QAC5BG,OAAO,EAAE;MACX;IACF,CAAC,EACD;MACExJ,GAAG,EAAE,iBAAiB;MACtBqJ,KAAK,EAAE,mBAAmB;MAC1BpL,GAAG,EAAE7B,kBAAkB;MACvB+K,OAAO,EAAE,IAAI;MACba,OAAO,EAAEA,CAAA,KAAM;QACb,IAAI,EAAClM,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEgB,eAAe,GAAE,OAAO,KAAK;QAC9C,OAAOhB,UAAU,CAACgB,eAAe,CAACM,IAAI,CAACC,IAAI,IAAIE,UAAU,CAACF,IAAI,CAACP,eAAe,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC;MAC7F,CAAC;MACDwM,cAAc,EAAE,IAAI;MACpBC,WAAW,EAAE;QACXF,KAAK,EAAE,mBAAmB;QAC1BG,OAAO,EAAE;MACX;IACF,CAAC,CACF;;IAED;IACA,OAAOJ,SAAS,CAACK,MAAM,CAACrI,KAAK,IAC3BvD,kBAAkB,CAACuD,KAAK,CAACpB,GAAG,CAAC,IAAIoB,KAAK,CAAC4G,OAAO,CAAC,CACjD,CAAC;EACH,CAAC;EAED,MAAM0B,SAAS,GAAGT,kBAAkB,CAAC,CAAC;EACtC;EACA,MAAMU,SAAS,GACb7K,IAAI,CAACC,KAAK,CACR,CAAC2K,SAAS,CAAClM,WAAW,GAAGkM,SAAS,CAAChM,SAAS,GAAGgM,SAAS,CAAC9L,aAAa,IACtE,GACF,CAAC,GAAG,GAAG;;EAET;EACA,MAAMqJ,aAAa,GAAGC,gBAAgB,CAAC,CAAC;;EAExC;EACA;EACA,MAAM0C,cAAc,GAAG3C,aAAa,CAACvI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAChD,MAAMmL,cAAc,GAAG5C,aAAa,CAACvI,KAAK,CAAC,CAAC,CAAC;;EAE7C;EACA,MAAMoL,WAAW,GAAI1I,KAAK,iBACxB5F,OAAA;IAAqBuO,SAAS,EAAC,8CAA8C;IAAAC,QAAA,gBAC3ExO,OAAA;MACEuO,SAAS,EAAC,2CAA2C;MACrD5H,KAAK,EAAEvG,mBAAoB;MAAAoO,QAAA,EAE1B5I,KAAK,CAACiI;IAAK;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eACN5O,OAAA;MAAKyC,GAAG,EAAEmD,KAAK,CAACnD;IAAI;MAAAgM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EAC1BhJ,KAAK,CAACkI,cAAc,iBACnB9N,OAAA;MAAKuO,SAAS,EAAC,4DAA4D;MAAAC,QAAA,gBACzExO,OAAA;QACEuO,SAAS,EAAC,wBAAwB;QAClC5H,KAAK,EAAE;UAAE,GAAGvG,mBAAmB;UAAE0G,UAAU,EAAE;QAAU,CAAE;QAAA0H,QAAA,EAExD5I,KAAK,CAACmI,WAAW,CAACF;MAAK;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,eACN5O,OAAA;QAAK2G,KAAK,EAAEtG,gBAAiB;QAAAmO,QAAA,EAC1B5I,KAAK,CAACmI,WAAW,CAACC;MAAO;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA,GApBOhJ,KAAK,CAACpB,GAAG;IAAAiK,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAqBd,CACN;EAED,oBACE5O,OAAA;IAAKuO,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClBxO,OAAA;MAAKuO,SAAS,EAAC,oEAAoE;MAAAC,QAAA,gBAEjFxO,OAAA;QAAKuO,SAAS,EAAC,2FAA2F;QAAAC,QAAA,gBACxGxO,OAAA;UACEuO,SAAS,EAAC,sCAAsC;UAChD5H,KAAK,EAAEzG,eAAgB;UAAAsO,QAAA,EACxB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL5O,OAAA;UAAGuO,SAAS,EAAC,2BAA2B;UAAC5H,KAAK,EAAEyG,iBAAiB,CAAC,CAAE;UAAAoB,QAAA,GACjExB,kBAAkB,CAAC1M,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEuO,WAAW,EAAEvO,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEwO,YAAY,CAAC,EAAC,KAAG,EAACxB,iBAAiB,CAAChN,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEiN,WAAW,CAAC;QAAA;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGN5O,OAAA;QAAKuO,SAAS,EAAC,qEAAqE;QAAAC,QAAA,gBAClFxO,OAAA;UAAKuO,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BxO,OAAA;YAAKuO,SAAS,EAAC,cAAc;YAC5B5H,KAAK,EAAE;cAAC,GAAGtG,gBAAgB;cAAEuG,QAAQ,EAAG,MAAM;cAAEY,KAAK,EAAG;YAAS,CAAE;YAAAgH,QAAA,EACnE;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvB5O,OAAA;YAAKuO,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAClDrL,YAAY,CAAC+K,SAAS,CAAClM,WAAW;UAAC;YAAAyM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN5O,OAAA;UACEuO,SAAS,EAAC,iBAAiB;UAC3B5H,KAAK,EAAE;YAAEoI,eAAe,EAAE;UAAU,CAAE;UAAAP,QAAA,gBAEtCxO,OAAA;YAAKuO,SAAS,EAAC,cAAc;YAC7B5H,KAAK,EAAE;cAAC,GAAGtG,gBAAgB;cAAEuG,QAAQ,EAAG,MAAM;cAAEY,KAAK,EAAG;YAAS,CAAE;YAAAgH,QAAA,EAClE;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC7B5O,OAAA;YAAKuO,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAClDrL,YAAY,CAAC+K,SAAS,CAAChM,SAAS;UAAC;YAAAuM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN5O,OAAA;UAAKuO,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BxO,OAAA;YAAKuO,SAAS,EAAC,cAAc;YAC7B5H,KAAK,EAAE;cAAC,GAAGtG,gBAAgB;cAAEuG,QAAQ,EAAG,MAAM;cAAEY,KAAK,EAAG;YAAS,CAAE;YAAAgH,QAAA,EAClE;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACxB5O,OAAA;YAAKuO,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAClDrL,YAAY,CAAC+K,SAAS,CAAC9L,aAAa;UAAC;YAAAqM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN5O,OAAA;UACEuO,SAAS,EAAC,iBAAiB;UAC3B5H,KAAK,EAAE;YAAEoI,eAAe,EAAE;UAAU,CAAE;UAAAP,QAAA,gBAEtCxO,OAAA;YAAKuO,SAAS,EAAC,cAAc;YAC7B5H,KAAK,EAAE;cAAC,GAAGtG,gBAAgB;cAAEuG,QAAQ,EAAG,MAAM;cAAEY,KAAK,EAAG;YAAS,CAAE;YAAAgH,QAAA,EAClE;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrB5O,OAAA;YAAKuO,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAClDrL,YAAY,CAACgL,SAAS;UAAC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLR,cAAc,CAAC/J,GAAG,CAACuB,KAAK,IAAI0I,WAAW,CAAC1I,KAAK,CAAC,CAAC;IAAA;MAAA6I,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAE7C,CAAC,EAGLP,cAAc,CAACnN,MAAM,GAAG,CAAC,iBACxBlB,OAAA;MAAKuO,SAAS,EAAC,gEAAgE;MAAAC,QAAA,gBAC7ExO,OAAA;QAAKuO,SAAS,EAAC,oGAAoG;QAAAC,QAAA,gBACjHxO,OAAA;UACEuO,SAAS,EAAC,sCAAsC;UAChD5H,KAAK,EAAEzG,eAAgB;UAAAsO,QAAA,EACxB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL5O,OAAA;UAAGuO,SAAS,EAAC,2BAA2B;UAAC5H,KAAK,EAAEyG,iBAAiB,CAAC,CAAE;UAAAoB,QAAA,GACjExB,kBAAkB,CAAC1M,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEuO,WAAW,EAAEvO,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEwO,YAAY,CAAC,EAAC,KAAG,EAACxB,iBAAiB,CAAChN,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEiN,WAAW,CAAC;QAAA;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAGLP,cAAc,CAAChK,GAAG,CAACuB,KAAK,IAAI0I,WAAW,CAAC1I,KAAK,CAAC,CAAC;IAAA;MAAA6I,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACpO,EAAA,CA7hCIP,mBAAmB;AAAA+O,EAAA,GAAnB/O,mBAAmB;AA+hCzB,eAAeA,mBAAmB;AAAC,IAAA+O,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}