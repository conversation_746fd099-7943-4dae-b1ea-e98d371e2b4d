import React, { useEffect, useRef } from 'react';
import ApexCharts from 'apexcharts';

const OperationalEfficiencyDashboard = ({
  headerTextStyle = {},
  subHeadingTextStyle = {},
  contentTextStyle = {},
  contentSettings = {},
  operationalData = null
}) => {
  const salesOutstandingRef = useRef(null);
  const payablesOutstandingRef = useRef(null);
  const inventoryOutstandingRef = useRef(null);
  const cashConversionRef = useRef(null);
  const fixedAssetTurnoverRef = useRef(null);

  // Function to check if a chart should be displayed based on content settings
  const shouldDisplayChart = (chartKey) => {
    if (!contentSettings?.chartSettings) return true; // Default to true if no settings
    return contentSettings.chartSettings[chartKey] === true;
  };

  // Enhanced data validation function - more lenient approach like FiscalYear
  const isDataLoaded = () => {
    if (!operationalData) {
      return false;
    }

    // Check if at least one of the required data arrays exists and has content
    const hasSalesOutstandingData = operationalData.daysSalesAROutstanding &&
      Array.isArray(operationalData.daysSalesAROutstanding) &&
      operationalData.daysSalesAROutstanding.length > 0;

    const hasPayablesOutstandingData = operationalData.daysSalesAPOutstanding &&
      Array.isArray(operationalData.daysSalesAPOutstanding) &&
      operationalData.daysSalesAPOutstanding.length > 0;

    const hasInventoryOutstandingData = operationalData.daysInventoryOutstanding &&
      Array.isArray(operationalData.daysInventoryOutstanding) &&
      operationalData.daysInventoryOutstanding.length > 0;

    const hasCashConversionData = operationalData.cashConversionCycle &&
      Array.isArray(operationalData.cashConversionCycle) &&
      operationalData.cashConversionCycle.length > 0;

    const hasFixedAssetTurnoverData = operationalData.fixedAssetTurnover &&
      Array.isArray(operationalData.fixedAssetTurnover) &&
      operationalData.fixedAssetTurnover.length > 0;

    return hasSalesOutstandingData || hasPayablesOutstandingData || hasInventoryOutstandingData ||
      hasCashConversionData || hasFixedAssetTurnoverData;
  };

  // Function to check if we have any meaningful data (not all zeros) - like FiscalYear

  useEffect(() => {
    if (isDataLoaded()) {
      // Clear charts first
      [salesOutstandingRef, payablesOutstandingRef, inventoryOutstandingRef, cashConversionRef, fixedAssetTurnoverRef].forEach((ref) => {
        if (ref.current) {
          ref.current.innerHTML = "";
        }
      });
      // Initialize charts with new data
      initializeCharts();
    }
  }, [operationalData, contentSettings]);

  const formatMonthYear = (year, month) => {
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return `${monthNames[month - 1]} ${String(year).slice(-2)}`;
  };

  const initializeCharts = () => {
    if (!operationalData) return;

    // Try to find the correct data structure
    let dataToUse = operationalData;

    // Check if data is nested under different possible paths
    if (operationalData.operationalEfficiency) {
      dataToUse = operationalData.operationalEfficiency;
    } else if (operationalData.operational) {
      dataToUse = operationalData.operational;
    } else if (operationalData.data) {
      dataToUse = operationalData.data;
    } else if (operationalData.reportData) {
      dataToUse = operationalData.reportData;
    }

    // Prepare data from API response - using correct property names from your JSON
    const categories = dataToUse.daysSalesAROutstanding?.map(item =>
      formatMonthYear(item.year, item.month)
    ) || [];

    const daysSalesOutstandingData = dataToUse.daysSalesAROutstanding?.map(item => {
      const value = item.dso || item.days_sales_outstanding || item.daysSalesOutstanding || 0;
      return parseFloat(value) || 0;
    }) || [];

    const daysPayablesOutstandingData = dataToUse.daysSalesAPOutstanding?.map(item => {
      const value = item.dpo || item.days_payables_outstanding || item.daysPayablesOutstanding || 0;
      return parseFloat(value) || 0;
    }) || [];

    const daysInventoryOutstandingData = dataToUse.daysInventoryOutstanding?.map(item => {
      const value = item.dio || item.days_inventory_outstanding || item.daysInventoryOutstanding || 0;
      return parseFloat(value) || 0;
    }) || [];

    const cashConversionCycleData = dataToUse.cashConversionCycle?.map(item => {
      const value = parseFloat(item.CCC || item.ccc);
      return isNaN(value) || value === null ? 0 : value;
    }) || [];

    const fixedAssetTurnoverData = dataToUse.fixedAssetTurnover?.map(item =>
      parseFloat(item.fat) || 0
    ) || [];


    // Color scheme
    const colors = {
      salesOutstanding: '#2d6a9b',
      payablesOutstanding: '#565aa4',
      inventoryOutstanding: '#2a689a',
      cashConversion: '#ff6b47',
      fixedAssetTurnover: '#2d6a9b'
    };

    // 1. Days Sales (A/R) Outstanding Chart
    const salesOutstandingOptions = {
      series: [{
        name: 'Days Sales Outstanding',
        data: daysSalesOutstandingData
      }],
      chart: {
        type: 'area',
        height: 200,
        toolbar: { show: false },
        background: 'transparent',
        zoom: {
          enabled: false
        }
      },
      dataLabels: {
        enabled: true,
        formatter: function (val) {
          return Math.round(val);
        },
        style: {
          fontSize: '14px',
          colors: ['#333'],
          fontWeight: '500'
        },
        offsetY: -10,
        background: {
          enabled: false
        },
        dropShadow: {
          enabled: false
        }
      },
      stroke: {
        curve: 'smooth',
        width: 2,
        colors: [colors.salesOutstanding]
      },
      fill: {
        type: 'gradient',
        gradient: {
          shadeIntensity: 1,
          type: 'vertical',
          colorStops: [
            { offset: 0, color: colors.salesOutstanding, opacity: 0.4 },
            { offset: 100, color: colors.salesOutstanding, opacity: 0.1 }
          ]
        }
      },
      markers: {
        size: 4,
        colors: [colors.salesOutstanding],
        strokeColors: '#fff',
        strokeWidth: 2,
        hover: {
          size: 6
        }
      },
      xaxis: {
        categories: categories,
        labels: {
          style: {
            colors: '#666',
            fontSize: '14px'
          }
        },
        axisBorder: { show: false },
        axisTicks: { show: false }
      },
      yaxis: {
        show: false
      },
      colors: [colors.salesOutstanding],
      grid: {
        show: false,
        padding: {
          left: 25,
          right: 25,
          top: 20,
          bottom: 0
        }
      },
      legend: {
        show: false
      },
      tooltip: {
        y: {
          formatter: function (val) {
            return Math.round(val) + ' days';
          }
        }
      }
    };

    // 2. Days Payables (AP) Outstanding Chart
    const payablesOutstandingOptions = {
      series: [{
        name: 'Days Payables Outstanding',
        data: daysPayablesOutstandingData
      }],
      chart: {
        type: 'area',
        height: 200,
        toolbar: { show: false },
        background: 'transparent',
        parentHeightOffset: 0,
        sparkline: {
          enabled: false
        },
        zoom: {
          enabled: false
        }
      },
      dataLabels: {
        enabled: true,
        formatter: function (val) {
          return Math.round(val);
        },
        style: {
          fontSize: '14px',
          colors: ['#333'],
          fontWeight: '500'
        },
        offsetY: -10,
        background: {
          enabled: false
        },
        dropShadow: {
          enabled: false
        }
      },
      stroke: {
        curve: 'smooth',
        width: 2,
        colors: [colors.payablesOutstanding]
      },
      fill: {
        type: 'gradient',
        gradient: {
          shadeIntensity: 1,
          type: 'vertical',
          colorStops: [
            { offset: 0, color: colors.payablesOutstanding, opacity: 0.4 },
            { offset: 100, color: colors.payablesOutstanding, opacity: 0.1 }
          ]
        }
      },
      markers: {
        size: 4,
        colors: [colors.payablesOutstanding],
        strokeColors: '#fff',
        strokeWidth: 2,
        hover: {
          size: 6
        }
      },
      xaxis: {
        categories: categories,
        labels: {
          style: {
            colors: '#666',
            fontSize: '14px'
          }
        },
        axisBorder: { show: false },
        axisTicks: { show: false }
      },
      yaxis: {
        show: false
      },
      colors: [colors.payablesOutstanding],
      grid: {
        show: false,
        padding: {
          left: 25,
          right: 25,
          top: 20,
          bottom: 0
        }
      },
      legend: {
        show: false
      },
      tooltip: {
        y: {
          formatter: function (val) {
            return Math.round(val) + ' days';
          }
        }
      }
    };

    // 3. Days Inventory Outstanding Chart (Bar Chart)
    const inventoryOutstandingOptions = {
      series: [{
        name: 'Days Inventory Outstanding',
        data: daysInventoryOutstandingData
      }],
      chart: {
        type: 'bar',
        height: 300,
        toolbar: { show: false },
        background: 'transparent'
      },
      plotOptions: {
        bar: {
          columnWidth: '40%',
          dataLabels: {
            position: 'top'
          }
        }
      },
      dataLabels: {
        enabled: true,
        formatter: function (val) {
          return Math.round(val);
        },
        style: {
          fontSize: '14px',
          colors: ['#333'],
          fontWeight: '500'
        },
        offsetY: -20,
        background: {
          enabled: false
        },
        dropShadow: {
          enabled: false
        }
      },
      xaxis: {
        categories: categories,
        labels: {
          style: {
            colors: '#666',
            fontSize: '14px'
          }
        },
        axisBorder: { show: false },
        axisTicks: { show: false }
      },
      yaxis: {
        show: false
      },
      colors: [colors.inventoryOutstanding],
      grid: {
        show: false,
        padding: {
          left: 10,
          right: 10,
          top: 25,
          bottom: 0
        }
      },
      legend: {
        show: false
      },
      tooltip: {
        y: {
          formatter: function (val) {
            return Math.round(val) + ' days';
          }
        }
      }
    };

    // 4. Cash Conversion Cycle Chart
    const cashConversionOptions = {
      series: [{
        name: 'Cash Conversion Cycle',
        data: cashConversionCycleData
      }],
      chart: {
        type: 'line',
        height: 200,
        toolbar: { show: false },
        background: 'transparent',
        zoom: {
          enabled: false
        }
      },
      dataLabels: {
        enabled: true,
        formatter: function (val) {
          return Math.round(val);
        },
        style: {
          fontSize: '14px',
          colors: ['#333'],
          fontWeight: '500'
        },
        offsetY: -10,
        background: {
          enabled: false
        },
        dropShadow: {
          enabled: false
        }
      },
      stroke: {
        width: 2,
        colors: [colors.cashConversion]
      },
      markers: {
        size: 4,
        colors: [colors.cashConversion],
        strokeColors: '#fff',
        strokeWidth: 2,
        hover: {
          size: 6
        }
      },
      xaxis: {
        categories: categories,
        labels: {
          style: {
            colors: '#666',
            fontSize: '14px'
          }
        },
        axisBorder: { show: false },
        axisTicks: { show: false }
      },
      yaxis: {
        show: false
      },
      colors: [colors.cashConversion],
      grid: {
        show: false,
        padding: {
          left: 25,
          right: 25,
          top: 20,
          bottom: 0
        }
      },
      legend: {
        show: false
      },
      tooltip: {
        y: {
          formatter: function (val) {
            return Math.round(val) + ' days';
          }
        }
      }
    };

    // 5. Fixed Asset Turnover Chart
    const fixedAssetTurnoverOptions = {
      series: [{
        name: 'Fixed Asset Turnover',
        data: fixedAssetTurnoverData
      }],
      chart: {
        type: 'area',
        height: 200,
        toolbar: { show: false },
        background: 'transparent',
        zoom: {
          enabled: false
        }
      },
      dataLabels: {
        enabled: true,
        formatter: function (val) {
          return val.toFixed(2);
        },
        style: {
          fontSize: '14px',
          colors: ['#333'],
          fontWeight: '500'
        },
        offsetY: -10,
        background: {
          enabled: false
        },
        dropShadow: {
          enabled: false
        }
      },
      stroke: {
        curve: 'smooth',
        width: 2,
        colors: [colors.fixedAssetTurnover]
      },
      fill: {
        type: 'gradient',
        gradient: {
          shadeIntensity: 1,
          type: 'vertical',
          colorStops: [
            { offset: 0, color: colors.fixedAssetTurnover, opacity: 0.4 },
            { offset: 100, color: colors.fixedAssetTurnover, opacity: 0.1 }
          ]
        }
      },
      markers: {
        size: 4,
        colors: [colors.fixedAssetTurnover],
        strokeColors: '#fff',
        strokeWidth: 2,
        hover: {
          size: 6
        }
      },
      xaxis: {
        categories: categories,
        labels: {
          style: {
            colors: '#666',
            fontSize: '14px'
          }
        },
        axisBorder: { show: false },
        axisTicks: { show: false }
      },
      yaxis: {
        show: false
      },
      colors: [colors.fixedAssetTurnover],
      grid: {
        show: false,
        padding: {
          left: 15,
          right: 15,
          top: 20,
          bottom: 0
        }
      },
      legend: {
        show: false
      },
      tooltip: {
        y: {
          formatter: function (val) {
            return val.toFixed(2);
          }
        }
      }
    };

    // Clear existing charts before rendering new ones
    const clearAndRenderChart = (ref, options, chartName) => {
      if (ref.current) {
        // Clear any existing chart
        ref.current.innerHTML = '';

        // Wait a tick before rendering to ensure DOM is cleared
        setTimeout(() => {
          if (ref.current) {
            try {
              const chart = new ApexCharts(ref.current, options);
              chart.render();

              // Store chart instances globally for export
              if (chartName === "Days Sales Outstanding") {
                window.salesOutstandingChart = chart;
              } else if (chartName === "Days Payables Outstanding") {
                window.payablesOutstandingChart = chart;
              } else if (chartName === "Days Inventory Outstanding") {
                window.inventoryOutstandingChart = chart;
              } else if (chartName === "Cash Conversion Cycle") {
                window.cashConversionChart = chart;
              } else if (chartName === "Fixed Asset Turnover") {
                window.fixedAssetTurnoverChart = chart;
              }
            } catch (error) {
              console.error(`OperationalEfficiency - Error rendering ${chartName} chart:`, error);
              // Show error message in chart container
              ref.current.innerHTML = `<div class="flex items-center justify-center h-48 text-gray-500">Error loading ${chartName} chart</div>`;
            }
          }
        }, 10);
      }
    };

    // Get enabled charts and assign chart options
    const enabledCharts = getEnabledCharts();

    // Assign chart options to enabled charts
    enabledCharts.forEach(chart => {
      switch (chart.key) {
        case 'daysSalesOutstanding':
          chart.options = salesOutstandingOptions;
          chart.name = 'Days Sales Outstanding';
          break;
        case 'daysPayablesOutstanding':
          chart.options = payablesOutstandingOptions;
          chart.name = 'Days Payables Outstanding';
          break;
        case 'daysInventoryOutstanding':
          chart.options = inventoryOutstandingOptions;
          chart.name = 'Days Inventory Outstanding';
          break;
        case 'cashConversionCycle':
          chart.options = cashConversionOptions;
          chart.name = 'Cash Conversion Cycle';
          break;
        case 'fixedAssetTurnover':
          chart.options = fixedAssetTurnoverOptions;
          chart.name = 'Fixed Asset Turnover';
          break;
      }
    });

    // Helper function to check if data array has meaningful values (not all zeros)
    const hasMeaningfulData = (dataArray) => {
      return dataArray && dataArray.length > 0 && dataArray.some(val => parseFloat(val) !== 0);
    };

    // Render charts with fallback for empty/zero data
    enabledCharts.forEach(({ ref, options, name, key }) => {
      if (ref.current) {
        let hasData = false;

        // Check if chart has meaningful data based on chart type
        if (key === 'daysSalesOutstanding') {
          const daysSalesData = dataToUse?.daysSalesAROutstanding?.map(item => {
            const value = item.dso || item.days_sales_outstanding || item.daysSalesOutstanding || 0;
            return parseFloat(value) || 0;
          }) || [];
          hasData = hasMeaningfulData(daysSalesData);
        } else if (key === 'daysPayablesOutstanding') {
          const payablesData = dataToUse?.daysPayablesOutstanding?.map(item => {
            const value = item.dpo || item.days_payables_outstanding || item.daysPayablesOutstanding || 0;
            return parseFloat(value) || 0;
          }) || [];
          hasData = hasMeaningfulData(payablesData);
        } else if (key === 'daysInventoryOutstanding') {
          const inventoryData = dataToUse?.daysInventoryOutstanding?.map(item => {
            const value = item.dio || item.days_inventory_outstanding || item.daysInventoryOutstanding || 0;
            return parseFloat(value) || 0;
          }) || [];
          hasData = hasMeaningfulData(inventoryData);
        } else if (key === 'cashConversionCycle') {
          const cashConversionData = dataToUse?.cashConversionCycle?.map(item => {
            const value = item.ccc || item.cash_conversion_cycle || item.cashConversionCycle || 0;
            return parseFloat(value) || 0;
          }) || [];
          hasData = hasMeaningfulData(cashConversionData);
        } else if (key === 'fixedAssetTurnover') {
          const fixedAssetData = dataToUse?.fixedAssetTurnover?.map(item => {
            const value = item.fat || item.fixed_asset_turnover || item.fixedAssetTurnover || 0;
            return parseFloat(value) || 0;
          }) || [];
          hasData = hasMeaningfulData(fixedAssetData);
        }

        if (hasData) {
          clearAndRenderChart(ref, options, name);
        } else {
          ref.current.innerHTML = `<div class="flex items-center justify-center h-64 text-gray-500">No meaningful ${name.toLowerCase()} data available</div>`;
        }
      }
    });

    // Clear all chart containers that are not being used
    [salesOutstandingRef, payablesOutstandingRef, inventoryOutstandingRef, cashConversionRef, fixedAssetTurnoverRef].forEach((ref) => {
      if (ref.current && !enabledCharts.some(chart => chart.ref === ref)) {
        ref.current.innerHTML = "";
      }
    });
  };

  const formatHeaderPeriod = (startYear, startMonth) => {
    const monthNames = [
      "January", "February", "March", "April", "May", "June",
      "July", "August", "September", "October", "November", "December"
    ];

    if (!startYear || !startMonth) {
      return " "; // fallback
    }

    const startMonthName = monthNames[startMonth - 1]; // Convert 1-based to 0-based index

    return `${startMonthName} ${startYear}`;
  };

  const formatHeaderStyle = () => {
    const style = { ...headerTextStyle };
    
    if (style.fontSize) {
      const fontSize = parseInt(style.fontSize);
      style.fontSize = `${fontSize / 2}px`;
    }
    return style;
  };

  const formatCompanyName = (companyName) => {
    if (!companyName) return '';

    if (companyName.length > 15) {
      return companyName.substring(0, 15) + '...';
    }

    return companyName;
  };

  // Function to determine which charts should be rendered and their order
  const getEnabledCharts = () => {
    // Try to find the correct data structure
    let dataToUse = operationalData;
    if (operationalData?.operationalEfficiency) {
      dataToUse = operationalData.operationalEfficiency;
    } else if (operationalData?.operational) {
      dataToUse = operationalData.operational;
    } else if (operationalData?.data) {
      dataToUse = operationalData.data;
    } else if (operationalData?.reportData) {
      dataToUse = operationalData.reportData;
    }

    const allCharts = [
      {
        key: 'daysSalesOutstanding',
        title: 'Days Sales (A/R) Outstanding',
        ref: salesOutstandingRef,
        options: null, // Will be set in initializeCharts
        hasData: () => {
          const daysSalesOutstandingData = dataToUse?.daysSalesAROutstanding?.map(item => {
            const value = item.dso || item.days_sales_outstanding || item.daysSalesOutstanding || 0;
            return parseFloat(value) || 0;
          }) || [];
          return daysSalesOutstandingData.length > 0 && daysSalesOutstandingData.some(val => val > 0);
        }
      },
      {
        key: 'daysPayablesOutstanding',
        title: 'Days Payables (AP) Outstanding',
        ref: payablesOutstandingRef,
        options: null,
        hasData: () => {
          const daysPayablesOutstandingData = dataToUse?.daysSalesAPOutstanding?.map(item => {
            const value = item.dpo || item.days_payables_outstanding || item.daysPayablesOutstanding || 0;
            return parseFloat(value) || 0;
          }) || [];
          return daysPayablesOutstandingData.length > 0 && daysPayablesOutstandingData.some(val => val > 0);
        },
        hasDescription: true,
        description: {
          title: 'Days AR Outstanding & Days AP Outstanding',
          content: 'Average number of days it takes customers to pay for invoices/ average number of days it takes a company to pay its suppliers.'
        }
      },
      {
        key: 'daysInventoryOutstanding',
        title: 'Days Inventory Outstanding',
        ref: inventoryOutstandingRef,
        options: null,
        hasData: () => {
          const daysInventoryOutstandingData = dataToUse?.daysInventoryOutstanding?.map(item => {
            const value = item.dio || item.days_inventory_outstanding || item.daysInventoryOutstanding || 0;
            return parseFloat(value) || 0;
          }) || [];
          return daysInventoryOutstandingData.length > 0 && daysInventoryOutstandingData.some(val => val > 0);
        },
        // containerMarginBottom: 'mb-24'
      },
      {
        key: 'cashConversionCycle',
        title: 'Cash Conversion Cycle',
        ref: cashConversionRef,
        options: null,
        hasData: () => {
          const cashConversionCycleData = dataToUse?.cashConversionCycle?.map(item => {
            const value = parseFloat(item.CCC || item.ccc);
            return isNaN(value) || value === null ? 0 : value;
          }) || [];
          return cashConversionCycleData.length > 0 && cashConversionCycleData.some(val => val != null && val !== 0);
        },
        hasDescription: true,
        description: {
          title: 'Cash Conversion Cycle (CCC)',
          content: 'The time it takes a company to convert the money spent on inventory or production back into cash by selling its goods or services. A shorter CCC is better because it means less time that money is tied up in inventory or accounts receivable.'
        }
      },
      {
        key: 'fixedAssetTurnover',
        title: 'Fixed Asset Turnover',
        ref: fixedAssetTurnoverRef,
        options: null,
        hasData: () => {
          const fixedAssetTurnoverData = dataToUse?.fixedAssetTurnover?.map(item =>
            parseFloat(item.fat) || 0
          ) || [];
          return fixedAssetTurnoverData.length > 0 && fixedAssetTurnoverData.some(val => val > 0);
        },
        hasDescription: true,
        description: {
          title: 'Fixed Asset Turnover (FAT)',
          content: 'The ratio of a company\'s net sales to its average fixed assets over a specific period, usually a year. A higher ratio indicates that a company is using its fixed assets more efficiently, while a lower ratio suggests underutilization.'
        }
      }
    ];

    // Filter charts based on settings and data availability
    return allCharts.filter(chart =>
      shouldDisplayChart(chart.key) && chart.hasData()
    );
  };

  // Get enabled charts for dynamic layout
  const enabledCharts = getEnabledCharts();

  // Split charts between upper and lower divs
  // Upper div can hold up to 3 charts, lower div gets the rest
  const upperDivCharts = enabledCharts.slice(0, 3);
  const lowerDivCharts = enabledCharts.slice(3);

  // Helper function to render a chart component
  const renderChart = (chart) => (
    <div key={chart.key} className={`bg-white p-6 border-b-4 border-blue-900 ${chart.containerMarginBottom || ''}`}>
      <div
        className="text-2xl font-semibold text-teal-600 mb-5"
        style={subHeadingTextStyle}
      >
        {chart.title}
      </div>
      <div ref={chart.ref} className="mb-5"></div>
      {chart.hasDescription && (
        <div className="mt-5 text-xl text-gray-600 leading-relaxed rounded-lg pb-5">
          <div
            className="text-teal-600 text-2xl mb-2"
            style={{ ...subHeadingTextStyle, fontWeight: 'lighter' }}
          >
            {chart.description.title}
          </div>
          <div style={contentTextStyle}>
            {chart.description.content}
          </div>
        </div>
      )}
    </div>
  );

  // Don't render anything if no charts are enabled
  if (enabledCharts.length === 0) {
    return null;
  }

  return (
    <div className="p-5">
      <div className="max-w-6xl h-[400mm] mx-auto bg-white flex flex-col gap-6 p-10 mb-6">
        {/* Header Section */}
        <div className="component-header flex items-center justify-between gap-4  border-b-4 border-blue-900 pb-2">
          <h1
            className="text-4xl font-bold text-gray-800 m-0"
            style={headerTextStyle}
          >
            Operational Efficiency
          </h1>
          <p className="text-lg text-gray-600 m-0" style={formatHeaderStyle()}>
            {formatHeaderPeriod(operationalData?.FYStartYear, operationalData?.FYStartMonth)} | {formatCompanyName(operationalData?.companyName)}
          </p>
        </div>

        {/* Dynamically render charts for upper div (up to 3 charts) */}
        {upperDivCharts.map(chart => renderChart(chart))}

      </div>

      {/* Only render lower div if there are charts to display (4th chart onwards) */}
      {lowerDivCharts.length > 0 && (
        <div className="max-w-6xl h-[400mm] mx-auto bg-white flex flex-col gap-10 p-10">
          {/* Header Section */}
          <div className="component-header flex items-center justify-between gap-4 mb-8 border-b-4 pt-2 border-blue-900 pb-2">
            <h1
              className="text-4xl font-bold text-gray-800 m-0"
              style={headerTextStyle}
            >
              Operational Efficiency
            </h1>
            <p className="text-lg text-gray-600 m-0" style={formatHeaderStyle()}>
              {formatHeaderPeriod(operationalData?.FYStartYear, operationalData?.FYStartMonth)} | {formatCompanyName(operationalData?.companyName)}
            </p>
          </div>

          {/* Dynamically render charts for lower div */}
          {lowerDivCharts.map(chart => renderChart(chart))}
        </div>
      )}
    </div>
  );
};

export default OperationalEfficiencyDashboard;