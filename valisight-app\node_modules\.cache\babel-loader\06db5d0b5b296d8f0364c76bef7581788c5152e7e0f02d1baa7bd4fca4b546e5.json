{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Valisights\\\\valisight-app\\\\src\\\\pages\\\\reports\\\\ReportPages\\\\ExpenseSummary.jsx\",\n  _s = $RefreshSig$();\nimport { useEffect, useRef } from \"react\";\nimport ApexCharts from \"apexcharts\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ExpenseSummaryDashboard = ({\n  headerTextStyle = {},\n  subHeadingTextStyle = {},\n  contentTextStyle = {},\n  reportData = null,\n  contentSettings = null // Add contentSettings prop\n}) => {\n  _s();\n  const roaRoeRef = useRef(null);\n  const expensesPieRef = useRef(null);\n  const expensesMonthlyRef = useRef(null);\n  const wagesRevenueRef = useRef(null);\n\n  // Store chart instances for export functionality\n  const roaRoeChartRef = useRef(null);\n  const expensesPieChartRef = useRef(null);\n  const expensesMonthlyChartRef = useRef(null);\n  const wagesRevenueChartRef = useRef(null);\n\n  // Helper function to sort data with \"Other\" last\n  const sortDataWithOtherLast = data => {\n    return data.sort((a, b) => {\n      const aIsOther = (a.account_name || '').toLowerCase().includes('other');\n      const bIsOther = (b.account_name || '').toLowerCase().includes('other');\n      if (aIsOther && !bIsOther) return 1; // a (Other) goes after b\n      if (!aIsOther && bIsOther) return -1; // b (Other) goes after a\n      return 0; // maintain original order for non-Other items\n    });\n  };\n\n  // Function to check if a chart should be displayed based on content settings\n  const shouldDisplayChart = chartKey => {\n    if (!(contentSettings !== null && contentSettings !== void 0 && contentSettings.chartSettings)) return true; // Default to true if no settings\n    return contentSettings.chartSettings[chartKey] === true;\n  };\n\n  // Enhanced data validation function\n  const isDataLoaded = () => {\n    if (!reportData) {\n      return false;\n    }\n\n    // Check if at least some required data exists - make it more flexible\n    const hasRoeRoaData = reportData.roeRoa && Array.isArray(reportData.roeRoa) && reportData.roeRoa.length > 0;\n    const hasExpensesData = reportData.expensesTopAccounts && Array.isArray(reportData.expensesTopAccounts) && reportData.expensesTopAccounts.length > 0;\n\n    // For monthly expenses, check for new detailed breakdown data first, then fallback to performance data\n    const hasMonthlyExpensesData = reportData.expensesTopAccountsMonthly && Array.isArray(reportData.expensesTopAccountsMonthly) && reportData.expensesTopAccountsMonthly.length > 0;\n    const hasMonthlyData = hasMonthlyExpensesData || reportData.monthlyPerformanceBreakDown && Array.isArray(reportData.monthlyPerformanceBreakDown) && reportData.monthlyPerformanceBreakDown.length > 0;\n\n    // Return true if we have at least some data to work with\n    return hasRoeRoaData || hasExpensesData || hasMonthlyData;\n  };\n  useEffect(() => {\n    if (isDataLoaded()) {\n      initializeCharts();\n    }\n  }, [reportData, contentSettings]); // Add contentSettings to dependency array\n\n  // Helper function to format month-year\n  const formatMonthYear = (year, month) => {\n    const monthNames = [\"Jan\", \"Feb\", \"Mar\", \"Apr\", \"May\", \"Jun\", \"Jul\", \"Aug\", \"Sep\", \"Oct\", \"Nov\", \"Dec\"];\n    return `${monthNames[month - 1]} ${String(year).slice(-2)}`;\n  };\n\n  // Helper function to format currency values with appropriate units\n  const formatCurrency = (val, options = {}) => {\n    if (val === null || val === undefined || isNaN(val) || val === 0) {\n      return options.showZero ? '$0' : '';\n    }\n    const absVal = Math.abs(val);\n    if (absVal >= 1000000) {\n      // Trillions\n      return '$' + (val / 1000000).toFixed(1) + 't';\n    } else if (absVal >= 1000) {\n      // Millions\n      return '$' + (val / 1000).toFixed(1) + 'm';\n    } else {\n      // Thousands (since our data is already in thousands)\n      return '$' + val.toFixed(2) + 'k';\n    }\n  };\n\n  // Process ROE/ROA data from API\n  const processRoeRoaData = () => {\n    if (!(reportData !== null && reportData !== void 0 && reportData.roeRoa)) return {\n      roaData: [],\n      roeData: [],\n      categories: []\n    };\n    const roaData = reportData.roeRoa.map(item => {\n      const value = parseFloat(item.roa || 0);\n      return isNaN(value) ? 0 : value;\n    });\n    console.log(\"Roa Data\", roaData);\n    const roeData = reportData.roeRoa.map(item => {\n      const value = parseFloat(item.roe || 0);\n      return isNaN(value) ? 0 : value;\n    });\n\n    // Create categories from the data if available\n    const categories = reportData.roeRoa.map(item => {\n      if (item.year && item.month) {\n        return formatMonthYear(item.year, item.month);\n      }\n      return item.period || `Period ${reportData.roeRoa.indexOf(item) + 1}`;\n    });\n    return {\n      roaData,\n      roeData,\n      categories\n    };\n  };\n\n  // Process expenses pie chart data from API\n  const processExpensesPieData = () => {\n    if (!(reportData !== null && reportData !== void 0 && reportData.expensesTopAccounts)) return {\n      data: [],\n      labels: []\n    };\n\n    // Sort data with \"Other\" last\n    const sortedExpenses = sortDataWithOtherLast([...reportData.expensesTopAccounts]);\n    const data = sortedExpenses.map(item => {\n      const value = parseFloat(item.total_expense || 0);\n      return isNaN(value) ? 0 : value / 1000; // Convert to thousands\n    });\n    const labels = sortedExpenses.map(item => {\n      const expense = parseFloat(item.total_expense || 0);\n      const percentage = parseFloat(item.percentage_of_total || 0);\n      const expenseDisplay = isNaN(expense) ? \"0.0\" : (expense / 1000).toFixed(2); // Fixed to 1 decimal place\n      const percentageDisplay = isNaN(percentage) ? \"0.0\" : percentage.toFixed(2); // Also fixed percentage to 1 decimal\n\n      return `${item.account_name || \"Unknown\"} ${expenseDisplay}k (${percentageDisplay}%)`;\n    });\n    return {\n      data,\n      labels\n    };\n  };\n\n  // Process monthly expenses data from API - use expensesTopAccountsMonthly for detailed account breakdown\n  const processMonthlyExpensesData = () => {\n    // Check if we have the new monthly expenses breakdown data\n    if (reportData !== null && reportData !== void 0 && reportData.expensesTopAccountsMonthly && Array.isArray(reportData.expensesTopAccountsMonthly)) {\n      return processDetailedMonthlyExpenses();\n    }\n\n    // Fallback to old method if new data is not available\n    if (!(reportData !== null && reportData !== void 0 && reportData.monthlyPerformanceBreakDown)) return {\n      series: [],\n      categories: []\n    };\n\n    // Get monthly data and create categories\n    const monthlyData = reportData.monthlyPerformanceBreakDown;\n    const categories = monthlyData.map(item => formatMonthYear(item.year, item.month));\n\n    // Since we don't have individual account breakdowns by month,\n    // we'll create a single series for total expenses\n    const totalExpensesData = monthlyData.map(item => {\n      const expense = parseFloat(item.totalExpenses || 0) / 1000; // Convert to thousands\n      return isNaN(expense) ? 0 : expense;\n    });\n    const series = [{\n      name: 'Total Expenses',\n      data: totalExpensesData\n    }];\n    return {\n      series,\n      categories\n    };\n  };\n\n  // Process detailed monthly expenses breakdown by account\n  const processDetailedMonthlyExpenses = () => {\n    if (!(reportData !== null && reportData !== void 0 && reportData.expensesTopAccountsMonthly) || !Array.isArray(reportData.expensesTopAccountsMonthly)) {\n      return {\n        series: [],\n        categories: []\n      };\n    }\n\n    // The backend now returns normalized data (one row per account per month)\n    // We need to transform it into the format expected by the chart\n\n    // First, get all unique months and sort them chronologically\n    const monthsSet = new Set();\n    reportData.expensesTopAccountsMonthly.forEach(row => {\n      if (row.year && row.month) {\n        monthsSet.add(`${row.year}-${row.month.toString().padStart(2, \"0\")}`);\n      }\n    });\n    const sortedMonths = Array.from(monthsSet).sort();\n\n    // Create categories (month labels) from the sorted months\n    const categories = sortedMonths.map(monthKey => {\n      const [year, month] = monthKey.split(\"-\");\n      return formatMonthYear(parseInt(year), parseInt(month));\n    });\n\n    // Group data by account\n    const accountData = {};\n    reportData.expensesTopAccountsMonthly.forEach(row => {\n      const accountName = row.account_name || \"Unknown\";\n      const monthKey = `${row.year}-${row.month.toString().padStart(2, \"0\")}`;\n      if (!accountData[accountName]) {\n        accountData[accountName] = {\n          name: accountName,\n          total_expense: row.total_expense || 0,\n          monthlyData: {}\n        };\n      }\n      accountData[accountName].monthlyData[monthKey] = parseFloat(row.monthly_expense || 0) / 1000; // Convert to thousands\n    });\n\n    // Convert to array and sort with \"Other\" last\n    const accountsArray = Object.values(accountData);\n    const sortedAccounts = accountsArray.sort((a, b) => {\n      if (a.name === \"Other\") return 1;\n      if (b.name === \"Other\") return -1;\n      return b.total_expense - a.total_expense;\n    });\n\n    // Create series for each account\n    const series = sortedAccounts.map(account => {\n      const data = sortedMonths.map(monthKey => {\n        const value = account.monthlyData[monthKey] || 0;\n        return isNaN(value) ? 0 : value;\n      });\n      return {\n        name: account.name,\n        data: data\n      };\n    });\n    return {\n      series,\n      categories\n    };\n  };\n\n  // Process wages vs revenue data from monthly performance data\n  const processWagesRevenueData = () => {\n    // Check if we have the new wagesRevenueMonthWise data\n    if (reportData !== null && reportData !== void 0 && reportData.wagesRevenueMonthWise && Array.isArray(reportData.wagesRevenueMonthWise)) {\n      const wagesData = reportData.wagesRevenueMonthWise;\n      const income = wagesData.map(item => {\n        const value = parseFloat(item.income || 0) / 1000; // Convert to thousands for better display\n        return isNaN(value) ? 0 : value;\n      });\n\n      // Use the actual ga_salaries and sales_salaries from the API\n      const salariesGA = wagesData.map(item => {\n        const value = parseFloat(item.ga_salaries || 0) / 1000; // Convert to thousands for better display\n        return isNaN(value) ? 0 : value;\n      });\n      const salariesSales = wagesData.map(item => {\n        const value = parseFloat(item.sales_salaries || 0) / 1000; // Convert to thousands for better display\n        return isNaN(value) ? 0 : value;\n      });\n      const categories = wagesData.map(item => formatMonthYear(item.year, item.month));\n      return {\n        income,\n        salariesGA,\n        salariesSales,\n        categories\n      };\n    }\n\n    // Fallback to old method if new data is not available\n    if (!(reportData !== null && reportData !== void 0 && reportData.monthlyPerformanceBreakDown)) return {\n      income: [],\n      salariesGA: [],\n      salariesSales: [],\n      categories: []\n    };\n    const monthlyData = reportData.monthlyPerformanceBreakDown;\n    const income = monthlyData.map(item => {\n      const value = parseFloat(item.totalIncome || 0) / 1000000; // Convert to millions\n      return isNaN(value) ? 0 : value;\n    });\n    const salariesGA = monthlyData.map(item => {\n      const expenses = parseFloat(item.totalExpenses || 0);\n      const value = expenses * 0.3 / 1000000; // Convert to millions\n      return isNaN(value) ? 0 : value;\n    });\n    const salariesSales = monthlyData.map(item => {\n      const expenses = parseFloat(item.totalExpenses || 0);\n      const value = expenses * 0.2 / 1000000; // Convert to millions\n      return isNaN(value) ? 0 : value;\n    });\n    const categories = monthlyData.map(item => formatMonthYear(item.year, item.month));\n    return {\n      income,\n      salariesGA,\n      salariesSales,\n      categories\n    };\n  };\n  const formatWagesRevenueValue = val => {\n    if (val === null || val === undefined || isNaN(val) || val === 0) return \"0\";\n    const absVal = Math.abs(val);\n    if (absVal >= 1000) {\n      return (val / 1000).toFixed(1) + 'm';\n    } else if (absVal >= 1) {\n      return val.toFixed(2) + 'k';\n    } else {\n      return (val * 1000).toFixed(0); // Convert back to original value for small numbers\n    }\n  };\n  const initializeCharts = () => {\n    const {\n      roaData,\n      roeData,\n      categories: roaRoeCategories\n    } = processRoeRoaData();\n    const {\n      data: pieData,\n      labels: pieLabels\n    } = processExpensesPieData();\n    const {\n      series: monthlyExpensesSeries,\n      categories: monthlyCategories\n    } = processMonthlyExpensesData();\n    const {\n      income,\n      salariesGA,\n      salariesSales,\n      categories: wagesCategories\n    } = processWagesRevenueData();\n\n    // Chart colors\n    const colors = {\n      roaRoe: [\"#4a90e2\", \"#ff6b47\"],\n      expensesPie: [\"#1f4e79\", \"#20b2aa\", \"#ff7f50\", \"#4db6ac\", \"#95a5a6\", \"#5d6d7e\", \"#bdc3c7\", \"#ffab91\", \"#9575cd\", \"#ba68c8\", \"#90a4ae\"],\n      monthlyExpenses: [\"#1f4e79\", \"#20b2aa\", \"#ff7f50\", \"#4db6ac\", \"#95a5a6\", \"#5d6d7e\", \"#bdc3c7\", \"#ffab91\", \"#9575cd\", \"#ba68c8\", \"#90a4ae\", \"#4a6fa5\", \"#2d5f5f\", \"#5f9ea0\", \"#8b7d82\", \"#4682b4\", \"#b0c4de\", \"#dda0dd\", \"#87ceeb\", \"#f0e68c\", \"#d3d3d3\"],\n      wagesRevenue: [\"#20b2aa\", \"#4a4a9a\", \"#ff7f50\"]\n    };\n\n    // 1. ROA and ROE Line Chart\n    const roaRoeOptions = {\n      series: [{\n        name: \"ROA\",\n        data: roaData\n      }, {\n        name: \"ROE\",\n        data: roeData\n      }],\n      chart: {\n        type: \"line\",\n        height: 250,\n        // reduced height\n        toolbar: {\n          show: false\n        },\n        // removes menu like download, zoom, etc.\n        background: \"transparent\",\n        zoom: {\n          enabled: false\n        }\n      },\n      dataLabels: {\n        enabled: true,\n        formatter: function (val, opts) {\n          if (val === null || val === undefined || isNaN(val)) return \"0\";\n          return val + \"%\";\n        },\n        style: {\n          fontSize: \"14px\",\n          colors: [\"#333\"],\n          fontWeight: \"500\"\n        },\n        background: {\n          enabled: false\n        },\n        offsetY: 0 // default (we override below in dropShadow trick)\n      },\n      stroke: {\n        curve: \"smooth\",\n        width: 2\n      },\n      xaxis: {\n        categories: roaRoeCategories,\n        labels: {\n          style: {\n            colors: \"#666\",\n            fontSize: \"14px\"\n          },\n          offsetY: 8 // move months slightly lower (positive value pushes down)\n        },\n        axisBorder: {\n          show: false\n        },\n        axisTicks: {\n          show: false\n        }\n      },\n      yaxis: {\n        show: false\n      },\n      colors: colors.roaRoe,\n      markers: {\n        size: 4,\n        strokeColors: \"#fff\",\n        strokeWidth: 1,\n        hover: {\n          size: 6\n        }\n      },\n      legend: {\n        position: \"bottom\",\n        horizontalAlign: \"center\",\n        offsetY: 20,\n        // pushes legend further down\n        markers: {\n          width: 8,\n          height: 8,\n          radius: 4\n        },\n        labels: {\n          colors: [\"#333\"],\n          useSeriesColors: false,\n          fontSize: \"14px\"\n        },\n        itemMargin: {\n          horizontal: 20,\n          vertical: 0\n        }\n      },\n      tooltip: {\n        y: {\n          formatter: function (val) {\n            if (val === null || val === undefined || isNaN(val)) return \"0\";\n            return val + \"%\";\n          }\n        }\n      },\n      grid: {\n        show: false,\n        padding: {\n          left: 25,\n          right: 25,\n          top: 20,\n          bottom: 0\n        }\n      },\n      annotations: {\n        yaxis: [{\n          y: 0,\n          borderColor: \"#999\",\n          borderWidth: 1,\n          strokeDashArray: 0,\n          opacity: 1\n        }]\n      },\n      // ✅ Directly apply per-series offset here\n      dataLabels: {\n        enabled: true,\n        formatter: function (val, opts) {\n          if (val === null || val === undefined || isNaN(val)) return \"0\";\n          const offsets = [-10, 10]; // ROA up, ROE down\n          this.offsetY = offsets[opts.seriesIndex];\n          return val + \"%\";\n        },\n        style: {\n          fontSize: \"14px\",\n          colors: [\"#333\"],\n          fontWeight: \"500\"\n        },\n        background: {\n          enabled: false\n        }\n      }\n    };\n\n    // 2. Expenses Pie Chart\n    const expensesPieOptions = {\n      series: pieData,\n      chart: {\n        type: \"pie\",\n        height: 400,\n        toolbar: {\n          show: false\n        },\n        zoom: {\n          enabled: false\n        }\n      },\n      labels: pieLabels,\n      colors: colors.expensesPie,\n      dataLabels: {\n        enabled: true,\n        formatter: function (val, opts) {\n          if (!opts || !opts.w || !opts.w.globals || !opts.w.globals.series) return \"\";\n          const value = opts.w.globals.series[opts.seriesIndex];\n          return value.toFixed(1) + 'k'; // Fixed to 1 decimal place with 'k' suffix\n        },\n        style: {\n          fontSize: \"14px\",\n          colors: [\"#fff\"],\n          fontWeight: \"500\"\n        },\n        dropShadow: {\n          enabled: false\n        }\n      },\n      legend: {\n        position: \"right\",\n        fontSize: \"14px\",\n        fontWeight: \"400\",\n        markers: {\n          width: 10,\n          height: 10,\n          radius: 5\n        },\n        labels: {\n          colors: \"#333\",\n          useSeriesColors: false,\n          fontFamily: \"Calibri\"\n        },\n        itemMargin: {\n          horizontal: 5,\n          vertical: 3\n        },\n        offsetX: 0\n      },\n      plotOptions: {\n        pie: {\n          dataLabels: {\n            offset: 0\n          }\n        }\n      },\n      tooltip: {\n        y: {\n          formatter: function (val) {\n            return '$' + val.toFixed(1) + 'k'; // Fixed to 1 decimal place in tooltip too\n          }\n        }\n      },\n      stroke: {\n        show: false\n      },\n      responsive: [{\n        breakpoint: 768,\n        options: {\n          legend: {\n            position: \"bottom\"\n          }\n        }\n      }]\n    };\n\n    // 3. Monthly Expenses Stacked Chart\n    const expensesMonthlyOptions = {\n      series: monthlyExpensesSeries,\n      chart: {\n        type: \"bar\",\n        height: 450,\n        stacked: true,\n        toolbar: {\n          show: false\n        },\n        background: \"transparent\"\n      },\n      plotOptions: {\n        bar: {\n          horizontal: false,\n          columnWidth: \"60%\",\n          dataLabels: {\n            total: {\n              enabled: true,\n              offsetY: -25,\n              style: {\n                fontSize: \"12px\",\n                fontWeight: \"600\",\n                color: \"#333\"\n              },\n              formatter: function (val) {\n                return formatCurrency(val);\n              }\n            }\n          }\n        }\n      },\n      dataLabels: {\n        enabled: false,\n        formatter: function (val) {\n          return formatCurrency(val);\n        },\n        style: {\n          fontSize: \"12px\",\n          fontWeight: \"500\",\n          colors: [\"#333\"]\n        },\n        offsetY: -5,\n        background: {\n          enabled: false\n        },\n        dropShadow: {\n          enabled: false\n        }\n      },\n      xaxis: {\n        categories: monthlyCategories,\n        labels: {\n          style: {\n            colors: \"#666\",\n            fontSize: \"12px\"\n          }\n        },\n        axisBorder: {\n          show: false\n        },\n        axisTicks: {\n          show: false\n        }\n      },\n      yaxis: {\n        show: false\n      },\n      colors: colors.monthlyExpenses,\n      legend: {\n        position: \"bottom\",\n        fontSize: \"12px\",\n        fontWeight: \"400\",\n        markers: {\n          width: 8,\n          height: 8,\n          radius: 4\n        },\n        labels: {\n          colors: \"#333\",\n          useSeriesColors: true\n        },\n        itemMargin: {\n          horizontal: 8,\n          vertical: 3\n        },\n        offsetY: 10,\n        onItemClick: {\n          toggleDataSeries: true\n        },\n        onItemHover: {\n          highlightDataSeries: true\n        }\n      },\n      tooltip: {\n        y: {\n          formatter: function (val) {\n            return formatCurrency(val, {\n              showZero: true\n            });\n          }\n        }\n      },\n      grid: {\n        show: false,\n        padding: {\n          left: 25,\n          right: 25,\n          top: 20,\n          bottom: 0\n        }\n      },\n      stroke: {\n        show: false\n      }\n    };\n\n    // 4. Wages vs Revenue Chart (using real data from API)\n    const wagesRevenueOptions = {\n      series: [{\n        name: \"Income\",\n        type: \"line\",\n        data: income\n      }, {\n        name: \"Salaries - G&A\",\n        type: \"column\",\n        data: salariesGA\n      }, {\n        name: \"Salaries - Sales\",\n        type: \"column\",\n        data: salariesSales\n      }],\n      chart: {\n        height: 450,\n        type: \"line\",\n        stacked: true,\n        toolbar: {\n          show: false\n        },\n        background: \"transparent\",\n        zoom: {\n          enabled: false\n        }\n      },\n      dataLabels: {\n        enabled: true,\n        enabledOnSeries: [0],\n        // Only show labels on Income line (series 0)\n        formatter: function (val) {\n          if (val === null || val === undefined || isNaN(val)) return \"\";\n          return \"$\" + formatWagesRevenueValue(val);\n        },\n        style: {\n          fontSize: \"14px\",\n          colors: [\"#20b2aa\"],\n          // Teal color for Income line\n          fontWeight: \"500\"\n        },\n        offsetY: -10,\n        background: {\n          enabled: false\n        },\n        dropShadow: {\n          enabled: false\n        }\n      },\n      stroke: {\n        width: [2, 0, 0],\n        curve: \"smooth\"\n      },\n      plotOptions: {\n        bar: {\n          columnWidth: \"60%\",\n          height: 1900,\n          dataLabels: {\n            total: {\n              enabled: true,\n              // Enable total labels to show sum of both salary columns\n              offsetY: -20,\n              style: {\n                fontSize: \"14px\",\n                fontWeight: \"500\",\n                color: \"#333\"\n              },\n              formatter: function (val) {\n                if (val === null || val === undefined || isNaN(val)) return \"$0\";\n                return \"$\" + formatWagesRevenueValue(val);\n              }\n            }\n          }\n        }\n      },\n      fill: {\n        opacity: [1, 1, 1]\n      },\n      labels: wagesCategories,\n      markers: {\n        size: [5, 0, 0],\n        fontSize: \"14px\",\n        strokeColors: \"#fff\",\n        strokeWidth: 2,\n        fillOpacity: 1,\n        hover: {\n          size: 7\n        }\n      },\n      xaxis: {\n        labels: {\n          style: {\n            colors: \"#666\",\n            fontSize: \"14px\"\n          }\n        },\n        axisBorder: {\n          show: false\n        },\n        axisTicks: {\n          show: false\n        }\n      },\n      yaxis: {\n        show: false\n      },\n      colors: colors.wagesRevenue,\n      legend: {\n        position: \"bottom\",\n        horizontalAlign: \"center\",\n        fontSize: \"14px\",\n        fontWeight: \"400\",\n        markers: {\n          width: 8,\n          height: 8,\n          radius: 4\n        },\n        labels: {\n          colors: \"#333\",\n          useSeriesColors: false\n        },\n        itemMargin: {\n          horizontal: 15,\n          vertical: 4\n        },\n        offsetY: 10,\n        onItemClick: {\n          toggleDataSeries: false\n        },\n        onItemHover: {\n          highlightDataSeries: false\n        }\n      },\n      tooltip: {\n        shared: true,\n        intersect: false,\n        y: [{\n          formatter: function (val) {\n            if (val === null || val === undefined || isNaN(val)) return \"$0\";\n            return \"$\" + formatWagesRevenueValue(val);\n          }\n        }, {\n          formatter: function (val) {\n            if (val === null || val === undefined || isNaN(val)) return \"$0\";\n            return \"$\" + formatWagesRevenueValue(val);\n          }\n        }, {\n          formatter: function (val) {\n            if (val === null || val === undefined || isNaN(val)) return \"$0\";\n            return \"$\" + formatWagesRevenueValue(val);\n          }\n        }]\n      },\n      grid: {\n        show: false,\n        padding: {\n          left: 25,\n          right: 25,\n          top: 20,\n          bottom: 0\n        }\n      }\n    };\n\n    // Clear existing charts before rendering new ones\n    const clearAndRenderChart = (ref, options, chartName, chartInstanceRef = null) => {\n      if (ref.current) {\n        // Clear any existing chart\n        if (chartInstanceRef && chartInstanceRef.current) {\n          chartInstanceRef.current.destroy();\n          chartInstanceRef.current = null;\n        }\n        ref.current.innerHTML = \"\";\n\n        // Wait a tick before rendering to ensure DOM is cleared\n        setTimeout(() => {\n          if (ref.current) {\n            try {\n              const chart = new ApexCharts(ref.current, options);\n              chart.render();\n\n              // Store chart instance for export functionality\n              if (chartInstanceRef) {\n                chartInstanceRef.current = chart;\n              }\n\n              // Make all charts globally accessible for export\n              if (chartName === \"ROA/ROE\") {\n                window.roaRoeChart = chart;\n              } else if (chartName === \"Expenses Pie\") {\n                window.expensesPieChart = chart;\n              } else if (chartName === \"Monthly Expenses\") {\n                window.expensesMonthlyChart = chart;\n              } else if (chartName === \"Wages vs Revenue\") {\n                window.wagesRevenueChart = chart;\n              }\n            } catch (error) {\n              console.error(`ExpenseSummary - Error rendering ${chartName} chart:`, error);\n            }\n          }\n        }, 10);\n      }\n    };\n\n    // Get enabled charts and assign chart options\n    const enabledCharts = getEnabledCharts();\n\n    // Assign chart options to enabled charts\n    enabledCharts.forEach(chart => {\n      switch (chart.key) {\n        case 'roaAndRoe':\n          chart.options = roaRoeOptions;\n          chart.name = 'ROA/ROE';\n          chart.chartInstanceRef = roaRoeChartRef;\n          break;\n        case 'expensesTopAccounts':\n          chart.options = expensesPieOptions;\n          chart.name = 'Expenses Pie';\n          chart.chartInstanceRef = expensesPieChartRef;\n          break;\n        case 'expensesTopAccountsMonthly':\n          chart.options = expensesMonthlyOptions;\n          chart.name = 'Monthly Expenses';\n          chart.chartInstanceRef = expensesMonthlyChartRef;\n          break;\n        case 'expensesWagesVsRevenueMonthly':\n          chart.options = wagesRevenueOptions;\n          chart.name = 'Wages vs Revenue';\n          chart.chartInstanceRef = wagesRevenueChartRef;\n          break;\n      }\n    });\n\n    // Helper function to check if data array has meaningful values (not all zeros)\n    const hasMeaningfulData = dataArray => {\n      return dataArray && dataArray.length > 0 && dataArray.some(val => parseFloat(val) !== 0);\n    };\n\n    // Render charts with fallback for empty/zero data\n    enabledCharts.forEach(({\n      ref,\n      options,\n      name,\n      chartInstanceRef,\n      key\n    }) => {\n      if (ref.current) {\n        let hasData = false;\n\n        // Check if chart has meaningful data based on chart type\n        if (key === 'roaAndRoe') {\n          var _reportData$roa, _reportData$roe;\n          const roaData = (reportData === null || reportData === void 0 ? void 0 : (_reportData$roa = reportData.roa) === null || _reportData$roa === void 0 ? void 0 : _reportData$roa.map(item => parseFloat(item.roa || 0))) || [];\n          const roeData = (reportData === null || reportData === void 0 ? void 0 : (_reportData$roe = reportData.roe) === null || _reportData$roe === void 0 ? void 0 : _reportData$roe.map(item => parseFloat(item.roe || 0))) || [];\n          hasData = hasMeaningfulData(roaData) || hasMeaningfulData(roeData);\n        } else if (key === 'expensesTopAccounts') {\n          const {\n            data: pieData\n          } = processExpensesPieData();\n          hasData = pieData && pieData.length > 0 && pieData.some(val => parseFloat(val) !== 0);\n        } else if (key === 'expensesTopAccountsMonthly') {\n          const {\n            series: monthlyExpensesSeries\n          } = processMonthlyExpensesData();\n          hasData = monthlyExpensesSeries && monthlyExpensesSeries.length > 0 && monthlyExpensesSeries.some(series => series.data && series.data.some(val => parseFloat(val) !== 0));\n        } else if (key === 'expensesWagesVsRevenueMonthly') {\n          const {\n            income,\n            salariesGA,\n            salariesSales\n          } = processWagesRevenueData();\n          hasData = hasMeaningfulData(income) || hasMeaningfulData(salariesGA) || hasMeaningfulData(salariesSales);\n        }\n        if (hasData) {\n          clearAndRenderChart(ref, options, name, chartInstanceRef);\n        } else {\n          ref.current.innerHTML = `<div class=\"flex items-center justify-center h-64 text-gray-500\">No meaningful ${name.toLowerCase()} data available</div>`;\n        }\n      }\n    });\n\n    // Clear all chart containers that are not being used\n    [roaRoeRef, expensesPieRef, expensesMonthlyRef, wagesRevenueRef].forEach(ref => {\n      if (ref.current && !enabledCharts.some(chart => chart.ref === ref)) {\n        ref.current.innerHTML = \"\";\n      }\n    });\n  };\n\n  // Add a fallback if reportData exists but has no usable data\n  const hasAnyUsableData = () => {\n    const {\n      roaData,\n      roeData\n    } = processRoeRoaData();\n    const {\n      data: pieData\n    } = processExpensesPieData();\n    const {\n      series: monthlyExpensesSeries\n    } = processMonthlyExpensesData();\n    const {\n      income\n    } = processWagesRevenueData();\n    return roaData.length > 0 || roeData.length > 0 || pieData.length > 0 || monthlyExpensesSeries.length > 0 || income.length > 0;\n  };\n  const formatHeaderPeriod = (startYear, startMonth) => {\n    const monthNames = [\"January\", \"February\", \"March\", \"April\", \"May\", \"June\", \"July\", \"August\", \"September\", \"October\", \"November\", \"December\"];\n    if (!startYear || !startMonth) {\n      return \" \"; // fallback\n    }\n    const startMonthName = monthNames[startMonth - 1]; // Convert 1-based to 0-based index\n\n    return `${startMonthName} ${startYear}`;\n  };\n  const formatHeaderStyle = () => {\n    const style = {\n      ...headerTextStyle\n    };\n    if (style.fontSize) {\n      const fontSize = parseInt(style.fontSize);\n      style.fontSize = `${fontSize / 2}px`;\n    }\n    return style;\n  };\n  const formatCompanyName = companyName => {\n    if (!companyName) return '';\n    if (companyName.length > 15) {\n      return companyName.substring(0, 15) + '...';\n    }\n    return companyName;\n  };\n\n  // Function to determine which charts should be rendered and their order\n  const getEnabledCharts = () => {\n    const allCharts = [{\n      key: 'roaAndRoe',\n      title: 'Return on Assets and Equity',\n      ref: roaRoeRef,\n      options: null,\n      // Will be set in initializeCharts\n      hasData: () => {\n        const {\n          roaData,\n          roeData\n        } = processRoeRoaData();\n        return roaData.length > 0 || roeData.length > 0;\n      },\n      hasDescription: true,\n      description: {\n        sections: [{\n          title: 'Return on Assets',\n          content: `Indicates how well ${reportData === null || reportData === void 0 ? void 0 : reportData.companyName} is using capital invested in Assets to generate Total Income. The higher the return, the more productive and efficient management is in utilizing economic resources the business has.`\n        }, {\n          title: 'Return on Equity',\n          content: 'Indicates how efficient company management is at generating growth from its Equity financing. Because Equity is equal to a company\\'s Assets minus Liabilities, ROE is also considered the Return on Net Assets.'\n        }]\n      }\n    }, {\n      key: 'expensesTopAccounts',\n      title: 'Expenses: Top Accounts',\n      ref: expensesPieRef,\n      options: null,\n      hasData: () => {\n        const {\n          data: pieData\n        } = processExpensesPieData();\n        return pieData.length > 0;\n      },\n      titleMarginBottom: 'mb-20'\n    }, {\n      key: 'expensesTopAccountsMonthly',\n      title: 'Expenses: Top Accounts Monthly',\n      ref: expensesMonthlyRef,\n      options: null,\n      hasData: () => {\n        const {\n          series: monthlyExpensesSeries\n        } = processMonthlyExpensesData();\n        return monthlyExpensesSeries.length > 0;\n      },\n      containerClass: 'expenses-monthly-apex'\n    }, {\n      key: 'expensesWagesVsRevenueMonthly',\n      title: 'Expenses: Wages Vs Revenue Monthly',\n      ref: wagesRevenueRef,\n      options: null,\n      hasData: () => {\n        const {\n          income\n        } = processWagesRevenueData();\n        return income.length > 0;\n      },\n      containerMarginBottom: 'mb-2'\n    }];\n\n    // Filter charts based on settings and data availability\n    return allCharts.filter(chart => shouldDisplayChart(chart.key) && chart.hasData());\n  };\n\n  // Get enabled charts for dynamic layout\n  const enabledCharts = getEnabledCharts();\n\n  // Split charts between upper and lower divs\n  // Upper div can hold up to 2 charts, lower div gets the rest\n  const upperDivCharts = enabledCharts.slice(0, 2);\n  const lowerDivCharts = enabledCharts.slice(2);\n\n  // Helper function to render a chart component\n  const renderChart = chart => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `bg-white p-6 border-b-4 border-blue-900 ${chart.containerClass || ''}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `text-2xl font-semibold text-teal-600 mb-5 ${chart.titleMarginBottom || ''}`,\n      style: subHeadingTextStyle,\n      children: chart.title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1111,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: chart.ref,\n      className: chart.containerMarginBottom || ''\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1117,\n      columnNumber: 7\n    }, this), chart.hasDescription && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-6\",\n      children: chart.description.sections.map((section, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: index === chart.description.sections.length - 1 ? \"mb-8\" : \"mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-teal-600 text-xl font-semibold\",\n          style: {\n            ...subHeadingTextStyle,\n            fontWeight: \"lighter\"\n          },\n          children: section.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1122,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-700 leading-relaxed\",\n          style: contentTextStyle,\n          children: section.content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1128,\n          columnNumber: 15\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1121,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1119,\n      columnNumber: 9\n    }, this)]\n  }, chart.key, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1110,\n    columnNumber: 5\n  }, this);\n\n  // Don't render anything if no charts are enabled\n  if (enabledCharts.length === 0) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-5\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl h-[410mm] mx-auto bg-white flex flex-col gap-10 p-10 mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"component-header flex items-center justify-between gap-4 mb-8 border-b-4 border-blue-900 pb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-bold text-gray-800 m-0\",\n          style: headerTextStyle,\n          children: \"Expense Summary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1151,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 m-0\",\n          style: formatHeaderStyle(),\n          children: [formatHeaderPeriod(reportData === null || reportData === void 0 ? void 0 : reportData.FYStartYear, reportData === null || reportData === void 0 ? void 0 : reportData.FYStartMonth), \" | \", formatCompanyName(reportData === null || reportData === void 0 ? void 0 : reportData.companyName)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1157,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1150,\n        columnNumber: 9\n      }, this), upperDivCharts.map(chart => renderChart(chart))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1148,\n      columnNumber: 7\n    }, this), lowerDivCharts.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl h-[420mm] mx-auto bg-white flex flex-col gap-16 p-10\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"component-header flex items-center justify-between gap-4 mb-8 pt-2 border-b-4 border-blue-900 pb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-bold text-gray-800 m-0\",\n          style: headerTextStyle,\n          children: \"Expense Summary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1171,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 m-0\",\n          style: formatHeaderStyle(),\n          children: [formatHeaderPeriod(reportData === null || reportData === void 0 ? void 0 : reportData.FYStartYear, reportData === null || reportData === void 0 ? void 0 : reportData.FYStartMonth), \" | \", formatCompanyName(reportData === null || reportData === void 0 ? void 0 : reportData.companyName)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1177,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1170,\n        columnNumber: 11\n      }, this), lowerDivCharts.map(chart => renderChart(chart))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1169,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1147,\n    columnNumber: 5\n  }, this);\n};\n_s(ExpenseSummaryDashboard, \"O803nMjGA+VSacU2THbN+muBc1I=\");\n_c = ExpenseSummaryDashboard;\nexport default ExpenseSummaryDashboard;\nvar _c;\n$RefreshReg$(_c, \"ExpenseSummaryDashboard\");", "map": {"version": 3, "names": ["useEffect", "useRef", "Apex<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "ExpenseSummaryDashboard", "headerTextStyle", "subHeadingTextStyle", "contentTextStyle", "reportData", "contentSettings", "_s", "roaRoeRef", "expensesPieRef", "expensesMonthlyRef", "wagesRevenueRef", "roaRoeChartRef", "expensesPieChartRef", "expensesMonthlyChartRef", "wagesRevenueChartRef", "sortDataWithOtherLast", "data", "sort", "a", "b", "aIsOther", "account_name", "toLowerCase", "includes", "bIs<PERSON>ther", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chart<PERSON>ey", "chartSettings", "isDataLoaded", "hasRoeRoaData", "roe<PERSON><PERSON>", "Array", "isArray", "length", "hasExpensesData", "expensesTopAccounts", "hasMonthlyExpensesData", "expensesTopAccountsMonthly", "hasMonthlyData", "monthlyPerformanceBreakDown", "initializeCharts", "formatMonthYear", "year", "month", "monthNames", "String", "slice", "formatCurrency", "val", "options", "undefined", "isNaN", "showZero", "absVal", "Math", "abs", "toFixed", "processRoeRoaData", "roa<PERSON><PERSON>", "roeData", "categories", "map", "item", "value", "parseFloat", "roa", "console", "log", "roe", "period", "indexOf", "processExpensesPieData", "labels", "sortedExpenses", "total_expense", "expense", "percentage", "percentage_of_total", "expenseDisplay", "percentageDisplay", "processMonthlyExpensesData", "processDetailedMonthlyExpenses", "series", "monthlyData", "totalExpensesData", "totalExpenses", "name", "monthsSet", "Set", "for<PERSON>ach", "row", "add", "toString", "padStart", "sortedMonths", "from", "<PERSON><PERSON><PERSON>", "split", "parseInt", "accountData", "accountName", "monthly_expense", "accountsArray", "Object", "values", "sortedAccounts", "account", "processWagesRevenueData", "wagesRevenueMonthWise", "wagesData", "income", "salariesGA", "ga_salaries", "salariesSales", "sales_salaries", "totalIncome", "expenses", "formatWagesRevenueValue", "roaRoeCategories", "pieData", "<PERSON><PERSON><PERSON><PERSON>", "monthlyExpensesSeries", "monthlyCategories", "wagesCategories", "colors", "roa<PERSON>oe", "expensesPie", "monthlyExpenses", "wagesRevenue", "roaRoeOptions", "chart", "type", "height", "toolbar", "show", "background", "zoom", "enabled", "dataLabels", "formatter", "opts", "style", "fontSize", "fontWeight", "offsetY", "stroke", "curve", "width", "xaxis", "axisBorder", "axisTicks", "yaxis", "markers", "size", "strokeColors", "strokeWidth", "hover", "legend", "position", "horizontalAlign", "radius", "useSeriesColors", "itemMargin", "horizontal", "vertical", "tooltip", "y", "grid", "padding", "left", "right", "top", "bottom", "annotations", "borderColor", "borderWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "opacity", "offsets", "seriesIndex", "expensesPieOptions", "w", "globals", "dropShadow", "fontFamily", "offsetX", "plotOptions", "pie", "offset", "responsive", "breakpoint", "expensesMonthlyOptions", "stacked", "bar", "columnWidth", "total", "color", "onItemClick", "toggleDataSeries", "onItemHover", "highlightDataSeries", "wagesRevenueOptions", "enabledOnSeries", "fill", "fillOpacity", "shared", "intersect", "clearAndRender<PERSON>hart", "ref", "chartName", "chartInstanceRef", "current", "destroy", "innerHTML", "setTimeout", "render", "window", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "expensesMonthlyChart", "wagesRevenueChart", "error", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "hasMeaningfulData", "dataArray", "some", "hasData", "_reportData$roa", "_reportData$roe", "hasAnyUsableData", "formatHeaderPeriod", "startYear", "startMonth", "startMonthName", "formatHeaderStyle", "formatCompanyName", "companyName", "substring", "all<PERSON>hart<PERSON>", "title", "hasDescription", "description", "sections", "content", "titleMarginBottom", "containerClass", "containerMarginBottom", "filter", "upperDivCharts", "lowerDiv<PERSON><PERSON>s", "<PERSON><PERSON><PERSON>", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "section", "index", "FYStartYear", "FYStartMonth", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Valisights/valisight-app/src/pages/reports/ReportPages/ExpenseSummary.jsx"], "sourcesContent": ["import { useEffect, useRef } from \"react\";\r\nimport ApexCharts from \"apexcharts\";\r\n\r\nconst ExpenseSummaryDashboard = ({\r\n  headerTextStyle = {},\r\n  subHeadingTextStyle = {},\r\n  contentTextStyle = {},\r\n  reportData = null,\r\n  contentSettings = null, // Add contentSettings prop\r\n}) => {\r\n  const roaRoeRef = useRef(null);\r\n  const expensesPieRef = useRef(null);\r\n  const expensesMonthlyRef = useRef(null);\r\n  const wagesRevenueRef = useRef(null);\r\n\r\n  // Store chart instances for export functionality\r\n  const roaRoeChartRef = useRef(null);\r\n  const expensesPieChartRef = useRef(null);\r\n  const expensesMonthlyChartRef = useRef(null);\r\n  const wagesRevenueChartRef = useRef(null);\r\n\r\n  // Helper function to sort data with \"Other\" last\r\n  const sortDataWithOtherLast = (data) => {\r\n    return data.sort((a, b) => {\r\n      const aIsOther = (a.account_name || '').toLowerCase().includes('other');\r\n      const bIsOther = (b.account_name || '').toLowerCase().includes('other');\r\n\r\n      if (aIsOther && !bIsOther) return 1;  // a (Other) goes after b\r\n      if (!aIsOther && bIsOther) return -1; // b (Other) goes after a\r\n      return 0; // maintain original order for non-Other items\r\n    });\r\n  };\r\n\r\n  // Function to check if a chart should be displayed based on content settings\r\n  const shouldDisplayChart = (chartKey) => {\r\n    if (!contentSettings?.chartSettings) return true; // Default to true if no settings\r\n    return contentSettings.chartSettings[chartKey] === true;\r\n  };\r\n\r\n  // Enhanced data validation function\r\n  const isDataLoaded = () => {\r\n    if (!reportData) {\r\n      return false;\r\n    }\r\n\r\n    // Check if at least some required data exists - make it more flexible\r\n    const hasRoeRoaData = reportData.roeRoa &&\r\n      Array.isArray(reportData.roeRoa) &&\r\n      reportData.roeRoa.length > 0;\r\n\r\n    const hasExpensesData = reportData.expensesTopAccounts &&\r\n      Array.isArray(reportData.expensesTopAccounts) &&\r\n      reportData.expensesTopAccounts.length > 0;\r\n\r\n    // For monthly expenses, check for new detailed breakdown data first, then fallback to performance data\r\n    const hasMonthlyExpensesData = reportData.expensesTopAccountsMonthly &&\r\n      Array.isArray(reportData.expensesTopAccountsMonthly) &&\r\n      reportData.expensesTopAccountsMonthly.length > 0;\r\n\r\n    const hasMonthlyData = hasMonthlyExpensesData ||\r\n      (reportData.monthlyPerformanceBreakDown &&\r\n        Array.isArray(reportData.monthlyPerformanceBreakDown) &&\r\n        reportData.monthlyPerformanceBreakDown.length > 0);\r\n\r\n\r\n    // Return true if we have at least some data to work with\r\n    return hasRoeRoaData || hasExpensesData || hasMonthlyData;\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (isDataLoaded()) {\r\n      initializeCharts();\r\n    }\r\n  }, [reportData, contentSettings]); // Add contentSettings to dependency array\r\n\r\n  // Helper function to format month-year\r\n  const formatMonthYear = (year, month) => {\r\n    const monthNames = [\r\n      \"Jan\",\r\n      \"Feb\",\r\n      \"Mar\",\r\n      \"Apr\",\r\n      \"May\",\r\n      \"Jun\",\r\n      \"Jul\",\r\n      \"Aug\",\r\n      \"Sep\",\r\n      \"Oct\",\r\n      \"Nov\",\r\n      \"Dec\",\r\n    ];\r\n    return `${monthNames[month - 1]} ${String(year).slice(-2)}`;\r\n  };\r\n\r\n  // Helper function to format currency values with appropriate units\r\n  const formatCurrency = (val, options = {}) => {\r\n    if (val === null || val === undefined || isNaN(val) || val === 0) {\r\n      return options.showZero ? '$0' : '';\r\n    }\r\n\r\n    const absVal = Math.abs(val);\r\n\r\n    if (absVal >= 1000000) {\r\n      // Trillions\r\n      return '$' + (val / 1000000).toFixed(1) + 't';\r\n    } else if (absVal >= 1000) {\r\n      // Millions\r\n      return '$' + (val / 1000).toFixed(1) + 'm';\r\n    } else {\r\n      // Thousands (since our data is already in thousands)\r\n      return '$' + val.toFixed(2) + 'k';\r\n    }\r\n  };\r\n\r\n  // Process ROE/ROA data from API\r\n  const processRoeRoaData = () => {\r\n    if (!reportData?.roeRoa)\r\n      return { roaData: [], roeData: [], categories: [] };\r\n\r\n    const roaData = reportData.roeRoa.map((item) => {\r\n      const value = parseFloat(item.roa || 0);\r\n      return isNaN(value) ? 0 : value;\r\n    });\r\n    console.log(\"Roa Data\", roaData)\r\n\r\n    const roeData = reportData.roeRoa.map((item) => {\r\n      const value = parseFloat(item.roe || 0);\r\n      return isNaN(value) ? 0 : value;\r\n    });\r\n\r\n    // Create categories from the data if available\r\n    const categories = reportData.roeRoa.map((item) => {\r\n      if (item.year && item.month) {\r\n        return formatMonthYear(item.year, item.month);\r\n      }\r\n      return item.period || `Period ${reportData.roeRoa.indexOf(item) + 1}`;\r\n    });\r\n\r\n    return { roaData, roeData, categories };\r\n  };\r\n\r\n  // Process expenses pie chart data from API\r\n  const processExpensesPieData = () => {\r\n    if (!reportData?.expensesTopAccounts) return { data: [], labels: [] };\r\n\r\n    // Sort data with \"Other\" last\r\n    const sortedExpenses = sortDataWithOtherLast([...reportData.expensesTopAccounts]);\r\n\r\n    const data = sortedExpenses.map((item) => {\r\n      const value = parseFloat(item.total_expense || 0);\r\n      return isNaN(value) ? 0 : value / 1000; // Convert to thousands\r\n    });\r\n\r\n    const labels = sortedExpenses.map((item) => {\r\n      const expense = parseFloat(item.total_expense || 0);\r\n      const percentage = parseFloat(item.percentage_of_total || 0);\r\n      const expenseDisplay = isNaN(expense) ? \"0.0\" : (expense / 1000).toFixed(2); // Fixed to 1 decimal place\r\n      const percentageDisplay = isNaN(percentage) ? \"0.0\" : percentage.toFixed(2); // Also fixed percentage to 1 decimal\r\n\r\n      return `${item.account_name || \"Unknown\"\r\n        } ${expenseDisplay}k (${percentageDisplay}%)`;\r\n    });\r\n\r\n    return { data, labels };\r\n  };\r\n\r\n  // Process monthly expenses data from API - use expensesTopAccountsMonthly for detailed account breakdown\r\n  const processMonthlyExpensesData = () => {\r\n    // Check if we have the new monthly expenses breakdown data\r\n    if (reportData?.expensesTopAccountsMonthly && Array.isArray(reportData.expensesTopAccountsMonthly)) {\r\n      return processDetailedMonthlyExpenses();\r\n    }\r\n\r\n    // Fallback to old method if new data is not available\r\n    if (!reportData?.monthlyPerformanceBreakDown) return { series: [], categories: [] };\r\n\r\n    // Get monthly data and create categories\r\n    const monthlyData = reportData.monthlyPerformanceBreakDown;\r\n    const categories = monthlyData.map(item =>\r\n      formatMonthYear(item.year, item.month)\r\n    );\r\n\r\n    // Since we don't have individual account breakdowns by month,\r\n    // we'll create a single series for total expenses\r\n    const totalExpensesData = monthlyData.map(item => {\r\n      const expense = parseFloat(item.totalExpenses || 0) / 1000; // Convert to thousands\r\n      return isNaN(expense) ? 0 : expense;\r\n    });\r\n\r\n    const series = [{\r\n      name: 'Total Expenses',\r\n      data: totalExpensesData\r\n    }];\r\n\r\n    return { series, categories };\r\n  };\r\n\r\n  // Process detailed monthly expenses breakdown by account\r\n  const processDetailedMonthlyExpenses = () => {\r\n    if (\r\n      !reportData?.expensesTopAccountsMonthly ||\r\n      !Array.isArray(reportData.expensesTopAccountsMonthly)\r\n    ) {\r\n      return { series: [], categories: [] };\r\n    }\r\n\r\n    // The backend now returns normalized data (one row per account per month)\r\n    // We need to transform it into the format expected by the chart\r\n\r\n    // First, get all unique months and sort them chronologically\r\n    const monthsSet = new Set();\r\n    reportData.expensesTopAccountsMonthly.forEach((row) => {\r\n      if (row.year && row.month) {\r\n        monthsSet.add(`${row.year}-${row.month.toString().padStart(2, \"0\")}`);\r\n      }\r\n    });\r\n\r\n    const sortedMonths = Array.from(monthsSet).sort();\r\n\r\n    // Create categories (month labels) from the sorted months\r\n    const categories = sortedMonths.map((monthKey) => {\r\n      const [year, month] = monthKey.split(\"-\");\r\n      return formatMonthYear(parseInt(year), parseInt(month));\r\n    });\r\n\r\n    // Group data by account\r\n    const accountData = {};\r\n    reportData.expensesTopAccountsMonthly.forEach((row) => {\r\n      const accountName = row.account_name || \"Unknown\";\r\n      const monthKey = `${row.year}-${row.month.toString().padStart(2, \"0\")}`;\r\n\r\n      if (!accountData[accountName]) {\r\n        accountData[accountName] = {\r\n          name: accountName,\r\n          total_expense: row.total_expense || 0,\r\n          monthlyData: {},\r\n        };\r\n      }\r\n\r\n      accountData[accountName].monthlyData[monthKey] =\r\n        parseFloat(row.monthly_expense || 0) / 1000; // Convert to thousands\r\n    });\r\n\r\n    // Convert to array and sort with \"Other\" last\r\n    const accountsArray = Object.values(accountData);\r\n    const sortedAccounts = accountsArray.sort((a, b) => {\r\n      if (a.name === \"Other\") return 1;\r\n      if (b.name === \"Other\") return -1;\r\n      return b.total_expense - a.total_expense;\r\n    });\r\n\r\n    // Create series for each account\r\n    const series = sortedAccounts.map((account) => {\r\n      const data = sortedMonths.map((monthKey) => {\r\n        const value = account.monthlyData[monthKey] || 0;\r\n        return isNaN(value) ? 0 : value;\r\n      });\r\n\r\n      return {\r\n        name: account.name,\r\n        data: data,\r\n      };\r\n    });\r\n\r\n    return { series, categories };\r\n  };\r\n\r\n  // Process wages vs revenue data from monthly performance data\r\n  const processWagesRevenueData = () => {\r\n    // Check if we have the new wagesRevenueMonthWise data\r\n    if (reportData?.wagesRevenueMonthWise && Array.isArray(reportData.wagesRevenueMonthWise)) {\r\n      const wagesData = reportData.wagesRevenueMonthWise;\r\n\r\n      const income = wagesData.map((item) => {\r\n        const value = parseFloat(item.income || 0) / 1000; // Convert to thousands for better display\r\n        return isNaN(value) ? 0 : value;\r\n      });\r\n\r\n      // Use the actual ga_salaries and sales_salaries from the API\r\n      const salariesGA = wagesData.map((item) => {\r\n        const value = parseFloat(item.ga_salaries || 0) / 1000; // Convert to thousands for better display\r\n        return isNaN(value) ? 0 : value;\r\n      });\r\n\r\n      const salariesSales = wagesData.map((item) => {\r\n        const value = parseFloat(item.sales_salaries || 0) / 1000; // Convert to thousands for better display\r\n        return isNaN(value) ? 0 : value;\r\n      });\r\n\r\n      const categories = wagesData.map((item) =>\r\n        formatMonthYear(item.year, item.month)\r\n      );\r\n\r\n      return { income, salariesGA, salariesSales, categories };\r\n    }\r\n\r\n    // Fallback to old method if new data is not available\r\n    if (!reportData?.monthlyPerformanceBreakDown)\r\n      return { income: [], salariesGA: [], salariesSales: [], categories: [] };\r\n\r\n    const monthlyData = reportData.monthlyPerformanceBreakDown;\r\n\r\n    const income = monthlyData.map((item) => {\r\n      const value = parseFloat(item.totalIncome || 0) / 1000000; // Convert to millions\r\n      return isNaN(value) ? 0 : value;\r\n    });\r\n\r\n    const salariesGA = monthlyData.map((item) => {\r\n      const expenses = parseFloat(item.totalExpenses || 0);\r\n      const value = (expenses * 0.3) / 1000000; // Convert to millions\r\n      return isNaN(value) ? 0 : value;\r\n    });\r\n\r\n    const salariesSales = monthlyData.map((item) => {\r\n      const expenses = parseFloat(item.totalExpenses || 0);\r\n      const value = (expenses * 0.2) / 1000000; // Convert to millions\r\n      return isNaN(value) ? 0 : value;\r\n    });\r\n\r\n    const categories = monthlyData.map((item) =>\r\n      formatMonthYear(item.year, item.month)\r\n    );\r\n\r\n    return { income, salariesGA, salariesSales, categories };\r\n  };\r\n\r\n\r\n  const formatWagesRevenueValue = (val) => {\r\n    if (val === null || val === undefined || isNaN(val) || val === 0) return \"0\";\r\n\r\n    const absVal = Math.abs(val);\r\n\r\n    if (absVal >= 1000) {\r\n      return (val / 1000).toFixed(1) + 'm';\r\n    } else if (absVal >= 1) {\r\n      return val.toFixed(2) + 'k';\r\n    } else {\r\n      return (val * 1000).toFixed(0); // Convert back to original value for small numbers\r\n    }\r\n  };\r\n\r\n  const initializeCharts = () => {\r\n    const {\r\n      roaData,\r\n      roeData,\r\n      categories: roaRoeCategories,\r\n    } = processRoeRoaData();\r\n    const { data: pieData, labels: pieLabels } = processExpensesPieData();\r\n    const { series: monthlyExpensesSeries, categories: monthlyCategories } =\r\n      processMonthlyExpensesData();\r\n    const {\r\n      income,\r\n      salariesGA,\r\n      salariesSales,\r\n      categories: wagesCategories,\r\n    } = processWagesRevenueData();\r\n\r\n    // Chart colors\r\n    const colors = {\r\n      roaRoe: [\"#4a90e2\", \"#ff6b47\"],\r\n      expensesPie: [\r\n        \"#1f4e79\",\r\n        \"#20b2aa\",\r\n        \"#ff7f50\",\r\n        \"#4db6ac\",\r\n        \"#95a5a6\",\r\n        \"#5d6d7e\",\r\n        \"#bdc3c7\",\r\n        \"#ffab91\",\r\n        \"#9575cd\",\r\n        \"#ba68c8\",\r\n        \"#90a4ae\",\r\n      ],\r\n      monthlyExpenses: [\r\n        \"#1f4e79\",\r\n        \"#20b2aa\",\r\n        \"#ff7f50\",\r\n        \"#4db6ac\",\r\n        \"#95a5a6\",\r\n        \"#5d6d7e\",\r\n        \"#bdc3c7\",\r\n        \"#ffab91\",\r\n        \"#9575cd\",\r\n        \"#ba68c8\",\r\n        \"#90a4ae\",\r\n        \"#4a6fa5\",\r\n        \"#2d5f5f\",\r\n        \"#5f9ea0\",\r\n        \"#8b7d82\",\r\n        \"#4682b4\",\r\n        \"#b0c4de\",\r\n        \"#dda0dd\",\r\n        \"#87ceeb\",\r\n        \"#f0e68c\",\r\n        \"#d3d3d3\",\r\n      ],\r\n      wagesRevenue: [\"#20b2aa\", \"#4a4a9a\", \"#ff7f50\"],\r\n    };\r\n\r\n    // 1. ROA and ROE Line Chart\r\n    const roaRoeOptions = {\r\n      series: [\r\n        { name: \"ROA\", data: roaData },\r\n        { name: \"ROE\", data: roeData },\r\n      ],\r\n      chart: {\r\n        type: \"line\",\r\n        height: 250, // reduced height\r\n        toolbar: { show: false }, // removes menu like download, zoom, etc.\r\n        background: \"transparent\",\r\n        zoom: {\r\n          enabled: false\r\n        }\r\n      },\r\n      dataLabels: {\r\n        enabled: true,\r\n        formatter: function (val, opts) {\r\n          if (val === null || val === undefined || isNaN(val)) return \"0\";\r\n          return val + \"%\";\r\n        },\r\n        style: {\r\n          fontSize: \"14px\",\r\n          colors: [\"#333\"],\r\n          fontWeight: \"500\",\r\n        },\r\n        background: { enabled: false },\r\n        offsetY: 0, // default (we override below in dropShadow trick)\r\n      },\r\n      stroke: {\r\n        curve: \"smooth\",\r\n        width: 2,\r\n      },\r\n      xaxis: {\r\n        categories: roaRoeCategories,\r\n        labels: {\r\n          style: {\r\n            colors: \"#666\",\r\n            fontSize: \"14px\",\r\n          },\r\n          offsetY: 8 // move months slightly lower (positive value pushes down)\r\n        },\r\n        axisBorder: { show: false },\r\n        axisTicks: { show: false },\r\n      },\r\n\r\n      yaxis: { show: false },\r\n      colors: colors.roaRoe,\r\n      markers: {\r\n        size: 4,\r\n        strokeColors: \"#fff\",\r\n        strokeWidth: 1,\r\n        hover: { size: 6 },\r\n      },\r\n      legend: {\r\n        position: \"bottom\",\r\n        horizontalAlign: \"center\",\r\n        offsetY: 20, // pushes legend further down\r\n        markers: { width: 8, height: 8, radius: 4 },\r\n        labels: {\r\n          colors: [\"#333\"],\r\n          useSeriesColors: false,\r\n          fontSize: \"14px\",\r\n        },\r\n        itemMargin: { horizontal: 20, vertical: 0 },\r\n      },\r\n      tooltip: {\r\n        y: {\r\n          formatter: function (val) {\r\n            if (val === null || val === undefined || isNaN(val)) return \"0\";\r\n            return val + \"%\";\r\n          },\r\n        },\r\n      },\r\n      grid: {\r\n        show: false,\r\n        padding: { left: 25, right: 25, top: 20, bottom: 0 },\r\n      },\r\n      annotations: {\r\n        yaxis: [\r\n          {\r\n            y: 0,\r\n            borderColor: \"#999\",\r\n            borderWidth: 1,\r\n            strokeDashArray: 0,\r\n            opacity: 1,\r\n          },\r\n        ],\r\n      },\r\n      // ✅ Directly apply per-series offset here\r\n      dataLabels: {\r\n        enabled: true,\r\n        formatter: function (val, opts) {\r\n          if (val === null || val === undefined || isNaN(val)) return \"0\";\r\n          const offsets = [-10, 10]; // ROA up, ROE down\r\n          this.offsetY = offsets[opts.seriesIndex];\r\n          return val + \"%\";\r\n        },\r\n        style: {\r\n          fontSize: \"14px\",\r\n          colors: [\"#333\"],\r\n          fontWeight: \"500\",\r\n        },\r\n        background: { enabled: false },\r\n      },\r\n    };\r\n\r\n\r\n    // 2. Expenses Pie Chart\r\n    const expensesPieOptions = {\r\n      series: pieData,\r\n      chart: {\r\n        type: \"pie\",\r\n        height: 400,\r\n        toolbar: { show: false },\r\n        zoom: {\r\n          enabled: false\r\n        }\r\n      },\r\n      labels: pieLabels,\r\n      colors: colors.expensesPie,\r\n      dataLabels: {\r\n        enabled: true,\r\n        formatter: function (val, opts) {\r\n          if (!opts || !opts.w || !opts.w.globals || !opts.w.globals.series)\r\n            return \"\";\r\n          const value = opts.w.globals.series[opts.seriesIndex];\r\n          return value.toFixed(1) + 'k'; // Fixed to 1 decimal place with 'k' suffix\r\n        },\r\n        style: {\r\n          fontSize: \"14px\",\r\n          colors: [\"#fff\"],\r\n          fontWeight: \"500\",\r\n        },\r\n        dropShadow: {\r\n          enabled: false,\r\n        },\r\n      },\r\n      legend: {\r\n        position: \"right\",\r\n        fontSize: \"14px\",\r\n        fontWeight: \"400\",\r\n        markers: {\r\n          width: 10,\r\n          height: 10,\r\n          radius: 5,\r\n        },\r\n        labels: {\r\n          colors: \"#333\",\r\n          useSeriesColors: false,\r\n          fontFamily: \"Calibri\",\r\n        },\r\n        itemMargin: {\r\n          horizontal: 5,\r\n          vertical: 3,\r\n        },\r\n        offsetX: 0,\r\n      },\r\n      plotOptions: {\r\n        pie: {\r\n          dataLabels: {\r\n            offset: 0,\r\n          },\r\n        },\r\n      },\r\n      tooltip: {\r\n        y: {\r\n          formatter: function (val) {\r\n            return '$' + val.toFixed(1) + 'k'; // Fixed to 1 decimal place in tooltip too\r\n          },\r\n        },\r\n      },\r\n      stroke: {\r\n        show: false,\r\n      },\r\n      responsive: [\r\n        {\r\n          breakpoint: 768,\r\n          options: {\r\n            legend: { position: \"bottom\" },\r\n          },\r\n        },\r\n      ],\r\n    };\r\n\r\n    // 3. Monthly Expenses Stacked Chart\r\n    const expensesMonthlyOptions = {\r\n      series: monthlyExpensesSeries,\r\n      chart: {\r\n        type: \"bar\",\r\n        height: 450,\r\n        stacked: true,\r\n        toolbar: { show: false },\r\n        background: \"transparent\",\r\n      },\r\n      plotOptions: {\r\n        bar: {\r\n          horizontal: false,\r\n          columnWidth: \"60%\",\r\n          dataLabels: {\r\n            total: {\r\n              enabled: true,\r\n              offsetY: -25,\r\n              style: {\r\n                fontSize: \"12px\",\r\n                fontWeight: \"600\",\r\n                color: \"#333\",\r\n              },\r\n              formatter: function (val) {\r\n                return formatCurrency(val);\r\n              },\r\n            },\r\n          },\r\n        },\r\n      },\r\n      dataLabels: {\r\n        enabled: false,\r\n        formatter: function (val) {\r\n          return formatCurrency(val);\r\n        },\r\n        style: {\r\n          fontSize: \"12px\",\r\n          fontWeight: \"500\",\r\n          colors: [\"#333\"],\r\n        },\r\n        offsetY: -5,\r\n        background: {\r\n          enabled: false,\r\n        },\r\n        dropShadow: {\r\n          enabled: false,\r\n        },\r\n      },\r\n      xaxis: {\r\n        categories: monthlyCategories,\r\n        labels: {\r\n          style: {\r\n            colors: \"#666\",\r\n            fontSize: \"12px\",\r\n          },\r\n        },\r\n        axisBorder: {\r\n          show: false,\r\n        },\r\n        axisTicks: {\r\n          show: false,\r\n        },\r\n      },\r\n      yaxis: {\r\n        show: false,\r\n      },\r\n      colors: colors.monthlyExpenses,\r\n      legend: {\r\n        position: \"bottom\",\r\n        fontSize: \"12px\",\r\n        fontWeight: \"400\",\r\n        markers: {\r\n          width: 8,\r\n          height: 8,\r\n          radius: 4,\r\n        },\r\n        labels: {\r\n          colors: \"#333\",\r\n          useSeriesColors: true,\r\n        },\r\n        itemMargin: {\r\n          horizontal: 8,\r\n          vertical: 3,\r\n        },\r\n        offsetY: 10,\r\n        onItemClick: {\r\n          toggleDataSeries: true,\r\n        },\r\n        onItemHover: {\r\n          highlightDataSeries: true,\r\n        },\r\n      },\r\n      tooltip: {\r\n        y: {\r\n          formatter: function (val) {\r\n            return formatCurrency(val, { showZero: true });\r\n          },\r\n        },\r\n      },\r\n      grid: {\r\n        show: false,\r\n        padding: {\r\n          left: 25,\r\n          right: 25,\r\n          top: 20,\r\n          bottom: 0,\r\n        },\r\n      },\r\n      stroke: {\r\n        show: false,\r\n      },\r\n    };\r\n\r\n    // 4. Wages vs Revenue Chart (using real data from API)\r\n    const wagesRevenueOptions = {\r\n      series: [\r\n        {\r\n          name: \"Income\",\r\n          type: \"line\",\r\n          data: income,\r\n        },\r\n        {\r\n          name: \"Salaries - G&A\",\r\n          type: \"column\",\r\n          data: salariesGA,\r\n        },\r\n        {\r\n          name: \"Salaries - Sales\",\r\n          type: \"column\",\r\n          data: salariesSales,\r\n        },\r\n      ],\r\n      chart: {\r\n        height: 450,\r\n        type: \"line\",\r\n        stacked: true,\r\n        toolbar: { show: false },\r\n        background: \"transparent\",\r\n        zoom: {\r\n          enabled: false\r\n        }\r\n      },\r\n      dataLabels: {\r\n        enabled: true,\r\n        enabledOnSeries: [0], // Only show labels on Income line (series 0)\r\n        formatter: function (val) {\r\n          if (val === null || val === undefined || isNaN(val)) return \"\";\r\n          return \"$\" + formatWagesRevenueValue(val);\r\n        },\r\n        style: {\r\n          fontSize: \"14px\",\r\n          colors: [\"#20b2aa\"], // Teal color for Income line\r\n          fontWeight: \"500\",\r\n        },\r\n        offsetY: -10,\r\n        background: {\r\n          enabled: false,\r\n        },\r\n        dropShadow: {\r\n          enabled: false,\r\n        },\r\n      },\r\n      stroke: {\r\n        width: [2, 0, 0],\r\n        curve: \"smooth\",\r\n      },\r\n      plotOptions: {\r\n        bar: {\r\n          columnWidth: \"60%\",\r\n          height: 1900,\r\n          dataLabels: {\r\n            total: {\r\n              enabled: true, // Enable total labels to show sum of both salary columns\r\n              offsetY: -20,\r\n              style: {\r\n                fontSize: \"14px\",\r\n                fontWeight: \"500\",\r\n                color: \"#333\",\r\n              },\r\n              formatter: function (val) {\r\n                if (val === null || val === undefined || isNaN(val)) return \"$0\";\r\n                return \"$\" + formatWagesRevenueValue(val);\r\n              },\r\n            },\r\n          },\r\n        },\r\n      },\r\n      fill: {\r\n        opacity: [1, 1, 1],\r\n      },\r\n      labels: wagesCategories,\r\n      markers: {\r\n        size: [5, 0, 0],\r\n        fontSize: \"14px\",\r\n        strokeColors: \"#fff\",\r\n        strokeWidth: 2,\r\n        fillOpacity: 1,\r\n        hover: {\r\n          size: 7,\r\n        },\r\n      },\r\n      xaxis: {\r\n        labels: {\r\n          style: {\r\n            colors: \"#666\",\r\n            fontSize: \"14px\",\r\n          },\r\n        },\r\n        axisBorder: {\r\n          show: false,\r\n        },\r\n        axisTicks: {\r\n          show: false,\r\n        },\r\n      },\r\n      yaxis: {\r\n        show: false,\r\n      },\r\n      colors: colors.wagesRevenue,\r\n      legend: {\r\n        position: \"bottom\",\r\n        horizontalAlign: \"center\",\r\n        fontSize: \"14px\",\r\n        fontWeight: \"400\",\r\n        markers: {\r\n          width: 8,\r\n          height: 8,\r\n          radius: 4,\r\n        },\r\n        labels: {\r\n          colors: \"#333\",\r\n          useSeriesColors: false,\r\n        },\r\n        itemMargin: {\r\n          horizontal: 15,\r\n          vertical: 4,\r\n        },\r\n        offsetY: 10,\r\n        onItemClick: {\r\n          toggleDataSeries: false,\r\n        },\r\n        onItemHover: {\r\n          highlightDataSeries: false,\r\n        },\r\n      },\r\n      tooltip: {\r\n        shared: true,\r\n        intersect: false,\r\n        y: [\r\n          {\r\n            formatter: function (val) {\r\n              if (val === null || val === undefined || isNaN(val)) return \"$0\";\r\n              return \"$\" + formatWagesRevenueValue(val);\r\n            },\r\n          },\r\n          {\r\n            formatter: function (val) {\r\n              if (val === null || val === undefined || isNaN(val)) return \"$0\";\r\n              return \"$\" + formatWagesRevenueValue(val);\r\n            },\r\n          },\r\n          {\r\n            formatter: function (val) {\r\n              if (val === null || val === undefined || isNaN(val)) return \"$0\";\r\n              return \"$\" + formatWagesRevenueValue(val);\r\n            },\r\n          },\r\n        ],\r\n      },\r\n      grid: {\r\n        show: false,\r\n        padding: {\r\n          left: 25,\r\n          right: 25,\r\n          top: 20,\r\n          bottom: 0,\r\n        },\r\n      },\r\n    };\r\n\r\n\r\n    // Clear existing charts before rendering new ones\r\n    const clearAndRenderChart = (ref, options, chartName, chartInstanceRef = null) => {\r\n      if (ref.current) {\r\n        // Clear any existing chart\r\n        if (chartInstanceRef && chartInstanceRef.current) {\r\n          chartInstanceRef.current.destroy();\r\n          chartInstanceRef.current = null;\r\n        }\r\n        ref.current.innerHTML = \"\";\r\n\r\n        // Wait a tick before rendering to ensure DOM is cleared\r\n        setTimeout(() => {\r\n          if (ref.current) {\r\n            try {\r\n              const chart = new ApexCharts(ref.current, options);\r\n              chart.render();\r\n\r\n              // Store chart instance for export functionality\r\n              if (chartInstanceRef) {\r\n                chartInstanceRef.current = chart;\r\n              }\r\n\r\n              // Make all charts globally accessible for export\r\n              if (chartName === \"ROA/ROE\") {\r\n                window.roaRoeChart = chart;\r\n              } else if (chartName === \"Expenses Pie\") {\r\n                window.expensesPieChart = chart;\r\n              } else if (chartName === \"Monthly Expenses\") {\r\n                window.expensesMonthlyChart = chart;\r\n              } else if (chartName === \"Wages vs Revenue\") {\r\n                window.wagesRevenueChart = chart;\r\n              }\r\n            } catch (error) {\r\n              console.error(\r\n                `ExpenseSummary - Error rendering ${chartName} chart:`,\r\n                error\r\n              );\r\n            }\r\n          }\r\n        }, 10);\r\n      }\r\n    };\r\n\r\n    // Get enabled charts and assign chart options\r\n    const enabledCharts = getEnabledCharts();\r\n\r\n    // Assign chart options to enabled charts\r\n    enabledCharts.forEach(chart => {\r\n      switch (chart.key) {\r\n        case 'roaAndRoe':\r\n          chart.options = roaRoeOptions;\r\n          chart.name = 'ROA/ROE';\r\n          chart.chartInstanceRef = roaRoeChartRef;\r\n          break;\r\n        case 'expensesTopAccounts':\r\n          chart.options = expensesPieOptions;\r\n          chart.name = 'Expenses Pie';\r\n          chart.chartInstanceRef = expensesPieChartRef;\r\n          break;\r\n        case 'expensesTopAccountsMonthly':\r\n          chart.options = expensesMonthlyOptions;\r\n          chart.name = 'Monthly Expenses';\r\n          chart.chartInstanceRef = expensesMonthlyChartRef;\r\n          break;\r\n        case 'expensesWagesVsRevenueMonthly':\r\n          chart.options = wagesRevenueOptions;\r\n          chart.name = 'Wages vs Revenue';\r\n          chart.chartInstanceRef = wagesRevenueChartRef;\r\n          break;\r\n      }\r\n    });\r\n\r\n    // Helper function to check if data array has meaningful values (not all zeros)\r\n    const hasMeaningfulData = (dataArray) => {\r\n      return dataArray && dataArray.length > 0 && dataArray.some(val => parseFloat(val) !== 0);\r\n    };\r\n\r\n    // Render charts with fallback for empty/zero data\r\n    enabledCharts.forEach(({ ref, options, name, chartInstanceRef, key }) => {\r\n      if (ref.current) {\r\n        let hasData = false;\r\n\r\n        // Check if chart has meaningful data based on chart type\r\n        if (key === 'roaAndRoe') {\r\n          const roaData = reportData?.roa?.map(item => parseFloat(item.roa || 0)) || [];\r\n          const roeData = reportData?.roe?.map(item => parseFloat(item.roe || 0)) || [];\r\n          hasData = hasMeaningfulData(roaData) || hasMeaningfulData(roeData);\r\n        } else if (key === 'expensesTopAccounts') {\r\n          const { data: pieData } = processExpensesPieData();\r\n          hasData = pieData && pieData.length > 0 && pieData.some(val => parseFloat(val) !== 0);\r\n        } else if (key === 'expensesTopAccountsMonthly') {\r\n          const { series: monthlyExpensesSeries } = processMonthlyExpensesData();\r\n          hasData = monthlyExpensesSeries && monthlyExpensesSeries.length > 0 &&\r\n                   monthlyExpensesSeries.some(series => series.data && series.data.some(val => parseFloat(val) !== 0));\r\n        } else if (key === 'expensesWagesVsRevenueMonthly') {\r\n          const { income, salariesGA, salariesSales } = processWagesRevenueData();\r\n          hasData = hasMeaningfulData(income) || hasMeaningfulData(salariesGA) || hasMeaningfulData(salariesSales);\r\n        }\r\n\r\n        if (hasData) {\r\n          clearAndRenderChart(ref, options, name, chartInstanceRef);\r\n        } \r\n        else {\r\n          ref.current.innerHTML = `<div class=\"flex items-center justify-center h-64 text-gray-500\">No meaningful ${name.toLowerCase()} data available</div>`;\r\n        }\r\n      }\r\n    });\r\n\r\n    // Clear all chart containers that are not being used\r\n    [roaRoeRef, expensesPieRef, expensesMonthlyRef, wagesRevenueRef].forEach((ref) => {\r\n      if (ref.current && !enabledCharts.some(chart => chart.ref === ref)) {\r\n        ref.current.innerHTML = \"\";\r\n      }\r\n    });\r\n  };\r\n\r\n\r\n  // Add a fallback if reportData exists but has no usable data\r\n  const hasAnyUsableData = () => {\r\n    const { roaData, roeData } = processRoeRoaData();\r\n    const { data: pieData } = processExpensesPieData();\r\n    const { series: monthlyExpensesSeries } = processMonthlyExpensesData();\r\n    const { income } = processWagesRevenueData();\r\n\r\n    return (\r\n      roaData.length > 0 ||\r\n      roeData.length > 0 ||\r\n      pieData.length > 0 ||\r\n      monthlyExpensesSeries.length > 0 ||\r\n      income.length > 0\r\n    );\r\n  };\r\n\r\n  const formatHeaderPeriod = (startYear, startMonth) => {\r\n    const monthNames = [\r\n      \"January\", \"February\", \"March\", \"April\", \"May\", \"June\",\r\n      \"July\", \"August\", \"September\", \"October\", \"November\", \"December\"\r\n    ];\r\n\r\n    if (!startYear || !startMonth) {\r\n      return \" \"; // fallback\r\n    }\r\n\r\n    const startMonthName = monthNames[startMonth - 1]; // Convert 1-based to 0-based index\r\n\r\n    return `${startMonthName} ${startYear}`;\r\n  };\r\n\r\n  const formatHeaderStyle = () => {\r\n    const style = { ...headerTextStyle };\r\n    if (style.fontSize) {\r\n      const fontSize = parseInt(style.fontSize);\r\n      style.fontSize = `${fontSize / 2}px`;\r\n    }\r\n    return style;\r\n  };\r\n\r\n  const formatCompanyName = (companyName) => {\r\n    if (!companyName) return '';\r\n\r\n    if (companyName.length > 15) {\r\n      return companyName.substring(0, 15) + '...';\r\n    }\r\n\r\n    return companyName;\r\n  };\r\n\r\n  // Function to determine which charts should be rendered and their order\r\n  const getEnabledCharts = () => {\r\n    const allCharts = [\r\n      {\r\n        key: 'roaAndRoe',\r\n        title: 'Return on Assets and Equity',\r\n        ref: roaRoeRef,\r\n        options: null, // Will be set in initializeCharts\r\n        hasData: () => {\r\n          const { roaData, roeData } = processRoeRoaData();\r\n          return roaData.length > 0 || roeData.length > 0;\r\n        },\r\n        hasDescription: true,\r\n        description: {\r\n          sections: [\r\n            {\r\n              title: 'Return on Assets',\r\n              content: `Indicates how well ${reportData?.companyName} is using capital invested in Assets to generate Total Income. The higher the return, the more productive and efficient management is in utilizing economic resources the business has.`\r\n            },\r\n            {\r\n              title: 'Return on Equity',\r\n              content: 'Indicates how efficient company management is at generating growth from its Equity financing. Because Equity is equal to a company\\'s Assets minus Liabilities, ROE is also considered the Return on Net Assets.'\r\n            }\r\n          ]\r\n        }\r\n      },\r\n      {\r\n        key: 'expensesTopAccounts',\r\n        title: 'Expenses: Top Accounts',\r\n        ref: expensesPieRef,\r\n        options: null,\r\n        hasData: () => {\r\n          const { data: pieData } = processExpensesPieData();\r\n          return pieData.length > 0;\r\n        },\r\n        titleMarginBottom: 'mb-20'\r\n      },\r\n      {\r\n        key: 'expensesTopAccountsMonthly',\r\n        title: 'Expenses: Top Accounts Monthly',\r\n        ref: expensesMonthlyRef,\r\n        options: null,\r\n        hasData: () => {\r\n          const { series: monthlyExpensesSeries } = processMonthlyExpensesData();\r\n          return monthlyExpensesSeries.length > 0;\r\n        },\r\n        containerClass: 'expenses-monthly-apex'\r\n      },\r\n      {\r\n        key: 'expensesWagesVsRevenueMonthly',\r\n        title: 'Expenses: Wages Vs Revenue Monthly',\r\n        ref: wagesRevenueRef,\r\n        options: null,\r\n        hasData: () => {\r\n          const { income } = processWagesRevenueData();\r\n          return income.length > 0;\r\n        },\r\n        containerMarginBottom: 'mb-2'\r\n      }\r\n    ];\r\n\r\n    // Filter charts based on settings and data availability\r\n    return allCharts.filter(chart =>\r\n      shouldDisplayChart(chart.key) && chart.hasData()\r\n    );\r\n  };\r\n\r\n  // Get enabled charts for dynamic layout\r\n  const enabledCharts = getEnabledCharts();\r\n\r\n  // Split charts between upper and lower divs\r\n  // Upper div can hold up to 2 charts, lower div gets the rest\r\n  const upperDivCharts = enabledCharts.slice(0, 2);\r\n  const lowerDivCharts = enabledCharts.slice(2);\r\n\r\n  // Helper function to render a chart component\r\n  const renderChart = (chart) => (\r\n    <div key={chart.key} className={`bg-white p-6 border-b-4 border-blue-900 ${chart.containerClass || ''}`}>\r\n      <div\r\n        className={`text-2xl font-semibold text-teal-600 mb-5 ${chart.titleMarginBottom || ''}`}\r\n        style={subHeadingTextStyle}\r\n      >\r\n        {chart.title}\r\n      </div>\r\n      <div ref={chart.ref} className={chart.containerMarginBottom || ''}></div>\r\n      {chart.hasDescription && (\r\n        <div className=\"mt-6\">\r\n          {chart.description.sections.map((section, index) => (\r\n            <div key={index} className={index === chart.description.sections.length - 1 ? \"mb-8\" : \"mb-4\"}>\r\n              <div\r\n                className=\"text-teal-600 text-xl font-semibold\"\r\n                style={{ ...subHeadingTextStyle, fontWeight: \"lighter\" }}\r\n              >\r\n                {section.title}\r\n              </div>\r\n              <p\r\n                className=\"text-gray-700 leading-relaxed\"\r\n                style={contentTextStyle}\r\n              >\r\n                {section.content}\r\n              </p>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n\r\n  // Don't render anything if no charts are enabled\r\n  if (enabledCharts.length === 0) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div className=\"p-5\">\r\n      <div className=\"max-w-6xl h-[410mm] mx-auto bg-white flex flex-col gap-10 p-10 mb-4\">\r\n        {/* Header Section */}\r\n        <div className=\"component-header flex items-center justify-between gap-4 mb-8 border-b-4 border-blue-900 pb-2\">\r\n          <h1\r\n            className=\"text-4xl font-bold text-gray-800 m-0\"\r\n            style={headerTextStyle}\r\n          >\r\n            Expense Summary\r\n          </h1>\r\n          <p className=\"text-lg text-gray-600 m-0\" style={formatHeaderStyle()}>\r\n            {formatHeaderPeriod(reportData?.FYStartYear, reportData?.FYStartMonth)} | {formatCompanyName(reportData?.companyName)}\r\n          </p>\r\n        </div>\r\n\r\n        {/* Dynamically render charts for upper div */}\r\n        {upperDivCharts.map(chart => renderChart(chart))}\r\n\r\n      </div>\r\n\r\n      {/* Only render lower div if there are charts to display */}\r\n      {lowerDivCharts.length > 0 && (\r\n        <div className=\"max-w-6xl h-[420mm] mx-auto bg-white flex flex-col gap-16 p-10\">\r\n          <div className=\"component-header flex items-center justify-between gap-4 mb-8 pt-2 border-b-4 border-blue-900 pb-2\">\r\n            <h1\r\n              className=\"text-4xl font-bold text-gray-800 m-0\"\r\n              style={headerTextStyle}\r\n            >\r\n              Expense Summary\r\n            </h1>\r\n            <p className=\"text-lg text-gray-600 m-0\" style={formatHeaderStyle()}>\r\n              {formatHeaderPeriod(reportData?.FYStartYear, reportData?.FYStartMonth)} | {formatCompanyName(reportData?.companyName)}\r\n            </p>\r\n          </div>\r\n\r\n          {/* Dynamically render charts for lower div */}\r\n          {lowerDivCharts.map(chart => renderChart(chart))}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ExpenseSummaryDashboard;"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACzC,OAAOC,UAAU,MAAM,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,uBAAuB,GAAGA,CAAC;EAC/BC,eAAe,GAAG,CAAC,CAAC;EACpBC,mBAAmB,GAAG,CAAC,CAAC;EACxBC,gBAAgB,GAAG,CAAC,CAAC;EACrBC,UAAU,GAAG,IAAI;EACjBC,eAAe,GAAG,IAAI,CAAE;AAC1B,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,SAAS,GAAGX,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMY,cAAc,GAAGZ,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMa,kBAAkB,GAAGb,MAAM,CAAC,IAAI,CAAC;EACvC,MAAMc,eAAe,GAAGd,MAAM,CAAC,IAAI,CAAC;;EAEpC;EACA,MAAMe,cAAc,GAAGf,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMgB,mBAAmB,GAAGhB,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMiB,uBAAuB,GAAGjB,MAAM,CAAC,IAAI,CAAC;EAC5C,MAAMkB,oBAAoB,GAAGlB,MAAM,CAAC,IAAI,CAAC;;EAEzC;EACA,MAAMmB,qBAAqB,GAAIC,IAAI,IAAK;IACtC,OAAOA,IAAI,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACzB,MAAMC,QAAQ,GAAG,CAACF,CAAC,CAACG,YAAY,IAAI,EAAE,EAAEC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC;MACvE,MAAMC,QAAQ,GAAG,CAACL,CAAC,CAACE,YAAY,IAAI,EAAE,EAAEC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC;MAEvE,IAAIH,QAAQ,IAAI,CAACI,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAE;MACtC,IAAI,CAACJ,QAAQ,IAAII,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;MACtC,OAAO,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAIC,QAAQ,IAAK;IACvC,IAAI,EAACrB,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEsB,aAAa,GAAE,OAAO,IAAI,CAAC,CAAC;IAClD,OAAOtB,eAAe,CAACsB,aAAa,CAACD,QAAQ,CAAC,KAAK,IAAI;EACzD,CAAC;;EAED;EACA,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAACxB,UAAU,EAAE;MACf,OAAO,KAAK;IACd;;IAEA;IACA,MAAMyB,aAAa,GAAGzB,UAAU,CAAC0B,MAAM,IACrCC,KAAK,CAACC,OAAO,CAAC5B,UAAU,CAAC0B,MAAM,CAAC,IAChC1B,UAAU,CAAC0B,MAAM,CAACG,MAAM,GAAG,CAAC;IAE9B,MAAMC,eAAe,GAAG9B,UAAU,CAAC+B,mBAAmB,IACpDJ,KAAK,CAACC,OAAO,CAAC5B,UAAU,CAAC+B,mBAAmB,CAAC,IAC7C/B,UAAU,CAAC+B,mBAAmB,CAACF,MAAM,GAAG,CAAC;;IAE3C;IACA,MAAMG,sBAAsB,GAAGhC,UAAU,CAACiC,0BAA0B,IAClEN,KAAK,CAACC,OAAO,CAAC5B,UAAU,CAACiC,0BAA0B,CAAC,IACpDjC,UAAU,CAACiC,0BAA0B,CAACJ,MAAM,GAAG,CAAC;IAElD,MAAMK,cAAc,GAAGF,sBAAsB,IAC1ChC,UAAU,CAACmC,2BAA2B,IACrCR,KAAK,CAACC,OAAO,CAAC5B,UAAU,CAACmC,2BAA2B,CAAC,IACrDnC,UAAU,CAACmC,2BAA2B,CAACN,MAAM,GAAG,CAAE;;IAGtD;IACA,OAAOJ,aAAa,IAAIK,eAAe,IAAII,cAAc;EAC3D,CAAC;EAED3C,SAAS,CAAC,MAAM;IACd,IAAIiC,YAAY,CAAC,CAAC,EAAE;MAClBY,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAACpC,UAAU,EAAEC,eAAe,CAAC,CAAC,CAAC,CAAC;;EAEnC;EACA,MAAMoC,eAAe,GAAGA,CAACC,IAAI,EAAEC,KAAK,KAAK;IACvC,MAAMC,UAAU,GAAG,CACjB,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACN;IACD,OAAO,GAAGA,UAAU,CAACD,KAAK,GAAG,CAAC,CAAC,IAAIE,MAAM,CAACH,IAAI,CAAC,CAACI,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;EAC7D,CAAC;;EAED;EACA,MAAMC,cAAc,GAAGA,CAACC,GAAG,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;IAC5C,IAAID,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,IAAIC,KAAK,CAACH,GAAG,CAAC,IAAIA,GAAG,KAAK,CAAC,EAAE;MAChE,OAAOC,OAAO,CAACG,QAAQ,GAAG,IAAI,GAAG,EAAE;IACrC;IAEA,MAAMC,MAAM,GAAGC,IAAI,CAACC,GAAG,CAACP,GAAG,CAAC;IAE5B,IAAIK,MAAM,IAAI,OAAO,EAAE;MACrB;MACA,OAAO,GAAG,GAAG,CAACL,GAAG,GAAG,OAAO,EAAEQ,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;IAC/C,CAAC,MAAM,IAAIH,MAAM,IAAI,IAAI,EAAE;MACzB;MACA,OAAO,GAAG,GAAG,CAACL,GAAG,GAAG,IAAI,EAAEQ,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;IAC5C,CAAC,MAAM;MACL;MACA,OAAO,GAAG,GAAGR,GAAG,CAACQ,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;IACnC;EACF,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,EAACrD,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAE0B,MAAM,GACrB,OAAO;MAAE4B,OAAO,EAAE,EAAE;MAAEC,OAAO,EAAE,EAAE;MAAEC,UAAU,EAAE;IAAG,CAAC;IAErD,MAAMF,OAAO,GAAGtD,UAAU,CAAC0B,MAAM,CAAC+B,GAAG,CAAEC,IAAI,IAAK;MAC9C,MAAMC,KAAK,GAAGC,UAAU,CAACF,IAAI,CAACG,GAAG,IAAI,CAAC,CAAC;MACvC,OAAOd,KAAK,CAACY,KAAK,CAAC,GAAG,CAAC,GAAGA,KAAK;IACjC,CAAC,CAAC;IACFG,OAAO,CAACC,GAAG,CAAC,UAAU,EAAET,OAAO,CAAC;IAEhC,MAAMC,OAAO,GAAGvD,UAAU,CAAC0B,MAAM,CAAC+B,GAAG,CAAEC,IAAI,IAAK;MAC9C,MAAMC,KAAK,GAAGC,UAAU,CAACF,IAAI,CAACM,GAAG,IAAI,CAAC,CAAC;MACvC,OAAOjB,KAAK,CAACY,KAAK,CAAC,GAAG,CAAC,GAAGA,KAAK;IACjC,CAAC,CAAC;;IAEF;IACA,MAAMH,UAAU,GAAGxD,UAAU,CAAC0B,MAAM,CAAC+B,GAAG,CAAEC,IAAI,IAAK;MACjD,IAAIA,IAAI,CAACpB,IAAI,IAAIoB,IAAI,CAACnB,KAAK,EAAE;QAC3B,OAAOF,eAAe,CAACqB,IAAI,CAACpB,IAAI,EAAEoB,IAAI,CAACnB,KAAK,CAAC;MAC/C;MACA,OAAOmB,IAAI,CAACO,MAAM,IAAI,UAAUjE,UAAU,CAAC0B,MAAM,CAACwC,OAAO,CAACR,IAAI,CAAC,GAAG,CAAC,EAAE;IACvE,CAAC,CAAC;IAEF,OAAO;MAAEJ,OAAO;MAAEC,OAAO;MAAEC;IAAW,CAAC;EACzC,CAAC;;EAED;EACA,MAAMW,sBAAsB,GAAGA,CAAA,KAAM;IACnC,IAAI,EAACnE,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAE+B,mBAAmB,GAAE,OAAO;MAAEnB,IAAI,EAAE,EAAE;MAAEwD,MAAM,EAAE;IAAG,CAAC;;IAErE;IACA,MAAMC,cAAc,GAAG1D,qBAAqB,CAAC,CAAC,GAAGX,UAAU,CAAC+B,mBAAmB,CAAC,CAAC;IAEjF,MAAMnB,IAAI,GAAGyD,cAAc,CAACZ,GAAG,CAAEC,IAAI,IAAK;MACxC,MAAMC,KAAK,GAAGC,UAAU,CAACF,IAAI,CAACY,aAAa,IAAI,CAAC,CAAC;MACjD,OAAOvB,KAAK,CAACY,KAAK,CAAC,GAAG,CAAC,GAAGA,KAAK,GAAG,IAAI,CAAC,CAAC;IAC1C,CAAC,CAAC;IAEF,MAAMS,MAAM,GAAGC,cAAc,CAACZ,GAAG,CAAEC,IAAI,IAAK;MAC1C,MAAMa,OAAO,GAAGX,UAAU,CAACF,IAAI,CAACY,aAAa,IAAI,CAAC,CAAC;MACnD,MAAME,UAAU,GAAGZ,UAAU,CAACF,IAAI,CAACe,mBAAmB,IAAI,CAAC,CAAC;MAC5D,MAAMC,cAAc,GAAG3B,KAAK,CAACwB,OAAO,CAAC,GAAG,KAAK,GAAG,CAACA,OAAO,GAAG,IAAI,EAAEnB,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7E,MAAMuB,iBAAiB,GAAG5B,KAAK,CAACyB,UAAU,CAAC,GAAG,KAAK,GAAGA,UAAU,CAACpB,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE7E,OAAO,GAAGM,IAAI,CAACzC,YAAY,IAAI,SAAS,IAClCyD,cAAc,MAAMC,iBAAiB,IAAI;IACjD,CAAC,CAAC;IAEF,OAAO;MAAE/D,IAAI;MAAEwD;IAAO,CAAC;EACzB,CAAC;;EAED;EACA,MAAMQ,0BAA0B,GAAGA,CAAA,KAAM;IACvC;IACA,IAAI5E,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEiC,0BAA0B,IAAIN,KAAK,CAACC,OAAO,CAAC5B,UAAU,CAACiC,0BAA0B,CAAC,EAAE;MAClG,OAAO4C,8BAA8B,CAAC,CAAC;IACzC;;IAEA;IACA,IAAI,EAAC7E,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEmC,2BAA2B,GAAE,OAAO;MAAE2C,MAAM,EAAE,EAAE;MAAEtB,UAAU,EAAE;IAAG,CAAC;;IAEnF;IACA,MAAMuB,WAAW,GAAG/E,UAAU,CAACmC,2BAA2B;IAC1D,MAAMqB,UAAU,GAAGuB,WAAW,CAACtB,GAAG,CAACC,IAAI,IACrCrB,eAAe,CAACqB,IAAI,CAACpB,IAAI,EAAEoB,IAAI,CAACnB,KAAK,CACvC,CAAC;;IAED;IACA;IACA,MAAMyC,iBAAiB,GAAGD,WAAW,CAACtB,GAAG,CAACC,IAAI,IAAI;MAChD,MAAMa,OAAO,GAAGX,UAAU,CAACF,IAAI,CAACuB,aAAa,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;MAC5D,OAAOlC,KAAK,CAACwB,OAAO,CAAC,GAAG,CAAC,GAAGA,OAAO;IACrC,CAAC,CAAC;IAEF,MAAMO,MAAM,GAAG,CAAC;MACdI,IAAI,EAAE,gBAAgB;MACtBtE,IAAI,EAAEoE;IACR,CAAC,CAAC;IAEF,OAAO;MAAEF,MAAM;MAAEtB;IAAW,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMqB,8BAA8B,GAAGA,CAAA,KAAM;IAC3C,IACE,EAAC7E,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEiC,0BAA0B,KACvC,CAACN,KAAK,CAACC,OAAO,CAAC5B,UAAU,CAACiC,0BAA0B,CAAC,EACrD;MACA,OAAO;QAAE6C,MAAM,EAAE,EAAE;QAAEtB,UAAU,EAAE;MAAG,CAAC;IACvC;;IAEA;IACA;;IAEA;IACA,MAAM2B,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC3BpF,UAAU,CAACiC,0BAA0B,CAACoD,OAAO,CAAEC,GAAG,IAAK;MACrD,IAAIA,GAAG,CAAChD,IAAI,IAAIgD,GAAG,CAAC/C,KAAK,EAAE;QACzB4C,SAAS,CAACI,GAAG,CAAC,GAAGD,GAAG,CAAChD,IAAI,IAAIgD,GAAG,CAAC/C,KAAK,CAACiD,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;MACvE;IACF,CAAC,CAAC;IAEF,MAAMC,YAAY,GAAG/D,KAAK,CAACgE,IAAI,CAACR,SAAS,CAAC,CAACtE,IAAI,CAAC,CAAC;;IAEjD;IACA,MAAM2C,UAAU,GAAGkC,YAAY,CAACjC,GAAG,CAAEmC,QAAQ,IAAK;MAChD,MAAM,CAACtD,IAAI,EAAEC,KAAK,CAAC,GAAGqD,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC;MACzC,OAAOxD,eAAe,CAACyD,QAAQ,CAACxD,IAAI,CAAC,EAAEwD,QAAQ,CAACvD,KAAK,CAAC,CAAC;IACzD,CAAC,CAAC;;IAEF;IACA,MAAMwD,WAAW,GAAG,CAAC,CAAC;IACtB/F,UAAU,CAACiC,0BAA0B,CAACoD,OAAO,CAAEC,GAAG,IAAK;MACrD,MAAMU,WAAW,GAAGV,GAAG,CAACrE,YAAY,IAAI,SAAS;MACjD,MAAM2E,QAAQ,GAAG,GAAGN,GAAG,CAAChD,IAAI,IAAIgD,GAAG,CAAC/C,KAAK,CAACiD,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;MAEvE,IAAI,CAACM,WAAW,CAACC,WAAW,CAAC,EAAE;QAC7BD,WAAW,CAACC,WAAW,CAAC,GAAG;UACzBd,IAAI,EAAEc,WAAW;UACjB1B,aAAa,EAAEgB,GAAG,CAAChB,aAAa,IAAI,CAAC;UACrCS,WAAW,EAAE,CAAC;QAChB,CAAC;MACH;MAEAgB,WAAW,CAACC,WAAW,CAAC,CAACjB,WAAW,CAACa,QAAQ,CAAC,GAC5ChC,UAAU,CAAC0B,GAAG,CAACW,eAAe,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;IACjD,CAAC,CAAC;;IAEF;IACA,MAAMC,aAAa,GAAGC,MAAM,CAACC,MAAM,CAACL,WAAW,CAAC;IAChD,MAAMM,cAAc,GAAGH,aAAa,CAACrF,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAClD,IAAID,CAAC,CAACoE,IAAI,KAAK,OAAO,EAAE,OAAO,CAAC;MAChC,IAAInE,CAAC,CAACmE,IAAI,KAAK,OAAO,EAAE,OAAO,CAAC,CAAC;MACjC,OAAOnE,CAAC,CAACuD,aAAa,GAAGxD,CAAC,CAACwD,aAAa;IAC1C,CAAC,CAAC;;IAEF;IACA,MAAMQ,MAAM,GAAGuB,cAAc,CAAC5C,GAAG,CAAE6C,OAAO,IAAK;MAC7C,MAAM1F,IAAI,GAAG8E,YAAY,CAACjC,GAAG,CAAEmC,QAAQ,IAAK;QAC1C,MAAMjC,KAAK,GAAG2C,OAAO,CAACvB,WAAW,CAACa,QAAQ,CAAC,IAAI,CAAC;QAChD,OAAO7C,KAAK,CAACY,KAAK,CAAC,GAAG,CAAC,GAAGA,KAAK;MACjC,CAAC,CAAC;MAEF,OAAO;QACLuB,IAAI,EAAEoB,OAAO,CAACpB,IAAI;QAClBtE,IAAI,EAAEA;MACR,CAAC;IACH,CAAC,CAAC;IAEF,OAAO;MAAEkE,MAAM;MAAEtB;IAAW,CAAC;EAC/B,CAAC;;EAED;EACA,MAAM+C,uBAAuB,GAAGA,CAAA,KAAM;IACpC;IACA,IAAIvG,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEwG,qBAAqB,IAAI7E,KAAK,CAACC,OAAO,CAAC5B,UAAU,CAACwG,qBAAqB,CAAC,EAAE;MACxF,MAAMC,SAAS,GAAGzG,UAAU,CAACwG,qBAAqB;MAElD,MAAME,MAAM,GAAGD,SAAS,CAAChD,GAAG,CAAEC,IAAI,IAAK;QACrC,MAAMC,KAAK,GAAGC,UAAU,CAACF,IAAI,CAACgD,MAAM,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;QACnD,OAAO3D,KAAK,CAACY,KAAK,CAAC,GAAG,CAAC,GAAGA,KAAK;MACjC,CAAC,CAAC;;MAEF;MACA,MAAMgD,UAAU,GAAGF,SAAS,CAAChD,GAAG,CAAEC,IAAI,IAAK;QACzC,MAAMC,KAAK,GAAGC,UAAU,CAACF,IAAI,CAACkD,WAAW,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;QACxD,OAAO7D,KAAK,CAACY,KAAK,CAAC,GAAG,CAAC,GAAGA,KAAK;MACjC,CAAC,CAAC;MAEF,MAAMkD,aAAa,GAAGJ,SAAS,CAAChD,GAAG,CAAEC,IAAI,IAAK;QAC5C,MAAMC,KAAK,GAAGC,UAAU,CAACF,IAAI,CAACoD,cAAc,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;QAC3D,OAAO/D,KAAK,CAACY,KAAK,CAAC,GAAG,CAAC,GAAGA,KAAK;MACjC,CAAC,CAAC;MAEF,MAAMH,UAAU,GAAGiD,SAAS,CAAChD,GAAG,CAAEC,IAAI,IACpCrB,eAAe,CAACqB,IAAI,CAACpB,IAAI,EAAEoB,IAAI,CAACnB,KAAK,CACvC,CAAC;MAED,OAAO;QAAEmE,MAAM;QAAEC,UAAU;QAAEE,aAAa;QAAErD;MAAW,CAAC;IAC1D;;IAEA;IACA,IAAI,EAACxD,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEmC,2BAA2B,GAC1C,OAAO;MAAEuE,MAAM,EAAE,EAAE;MAAEC,UAAU,EAAE,EAAE;MAAEE,aAAa,EAAE,EAAE;MAAErD,UAAU,EAAE;IAAG,CAAC;IAE1E,MAAMuB,WAAW,GAAG/E,UAAU,CAACmC,2BAA2B;IAE1D,MAAMuE,MAAM,GAAG3B,WAAW,CAACtB,GAAG,CAAEC,IAAI,IAAK;MACvC,MAAMC,KAAK,GAAGC,UAAU,CAACF,IAAI,CAACqD,WAAW,IAAI,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC;MAC3D,OAAOhE,KAAK,CAACY,KAAK,CAAC,GAAG,CAAC,GAAGA,KAAK;IACjC,CAAC,CAAC;IAEF,MAAMgD,UAAU,GAAG5B,WAAW,CAACtB,GAAG,CAAEC,IAAI,IAAK;MAC3C,MAAMsD,QAAQ,GAAGpD,UAAU,CAACF,IAAI,CAACuB,aAAa,IAAI,CAAC,CAAC;MACpD,MAAMtB,KAAK,GAAIqD,QAAQ,GAAG,GAAG,GAAI,OAAO,CAAC,CAAC;MAC1C,OAAOjE,KAAK,CAACY,KAAK,CAAC,GAAG,CAAC,GAAGA,KAAK;IACjC,CAAC,CAAC;IAEF,MAAMkD,aAAa,GAAG9B,WAAW,CAACtB,GAAG,CAAEC,IAAI,IAAK;MAC9C,MAAMsD,QAAQ,GAAGpD,UAAU,CAACF,IAAI,CAACuB,aAAa,IAAI,CAAC,CAAC;MACpD,MAAMtB,KAAK,GAAIqD,QAAQ,GAAG,GAAG,GAAI,OAAO,CAAC,CAAC;MAC1C,OAAOjE,KAAK,CAACY,KAAK,CAAC,GAAG,CAAC,GAAGA,KAAK;IACjC,CAAC,CAAC;IAEF,MAAMH,UAAU,GAAGuB,WAAW,CAACtB,GAAG,CAAEC,IAAI,IACtCrB,eAAe,CAACqB,IAAI,CAACpB,IAAI,EAAEoB,IAAI,CAACnB,KAAK,CACvC,CAAC;IAED,OAAO;MAAEmE,MAAM;MAAEC,UAAU;MAAEE,aAAa;MAAErD;IAAW,CAAC;EAC1D,CAAC;EAGD,MAAMyD,uBAAuB,GAAIrE,GAAG,IAAK;IACvC,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,IAAIC,KAAK,CAACH,GAAG,CAAC,IAAIA,GAAG,KAAK,CAAC,EAAE,OAAO,GAAG;IAE5E,MAAMK,MAAM,GAAGC,IAAI,CAACC,GAAG,CAACP,GAAG,CAAC;IAE5B,IAAIK,MAAM,IAAI,IAAI,EAAE;MAClB,OAAO,CAACL,GAAG,GAAG,IAAI,EAAEQ,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;IACtC,CAAC,MAAM,IAAIH,MAAM,IAAI,CAAC,EAAE;MACtB,OAAOL,GAAG,CAACQ,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;IAC7B,CAAC,MAAM;MACL,OAAO,CAACR,GAAG,GAAG,IAAI,EAAEQ,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC;EACF,CAAC;EAED,MAAMhB,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAM;MACJkB,OAAO;MACPC,OAAO;MACPC,UAAU,EAAE0D;IACd,CAAC,GAAG7D,iBAAiB,CAAC,CAAC;IACvB,MAAM;MAAEzC,IAAI,EAAEuG,OAAO;MAAE/C,MAAM,EAAEgD;IAAU,CAAC,GAAGjD,sBAAsB,CAAC,CAAC;IACrE,MAAM;MAAEW,MAAM,EAAEuC,qBAAqB;MAAE7D,UAAU,EAAE8D;IAAkB,CAAC,GACpE1C,0BAA0B,CAAC,CAAC;IAC9B,MAAM;MACJ8B,MAAM;MACNC,UAAU;MACVE,aAAa;MACbrD,UAAU,EAAE+D;IACd,CAAC,GAAGhB,uBAAuB,CAAC,CAAC;;IAE7B;IACA,MAAMiB,MAAM,GAAG;MACbC,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;MAC9BC,WAAW,EAAE,CACX,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;MACDC,eAAe,EAAE,CACf,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;MACDC,YAAY,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;IAChD,CAAC;;IAED;IACA,MAAMC,aAAa,GAAG;MACpB/C,MAAM,EAAE,CACN;QAAEI,IAAI,EAAE,KAAK;QAAEtE,IAAI,EAAE0C;MAAQ,CAAC,EAC9B;QAAE4B,IAAI,EAAE,KAAK;QAAEtE,IAAI,EAAE2C;MAAQ,CAAC,CAC/B;MACDuE,KAAK,EAAE;QACLC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,GAAG;QAAE;QACbC,OAAO,EAAE;UAAEC,IAAI,EAAE;QAAM,CAAC;QAAE;QAC1BC,UAAU,EAAE,aAAa;QACzBC,IAAI,EAAE;UACJC,OAAO,EAAE;QACX;MACF,CAAC;MACDC,UAAU,EAAE;QACVD,OAAO,EAAE,IAAI;QACbE,SAAS,EAAE,SAAAA,CAAU3F,GAAG,EAAE4F,IAAI,EAAE;UAC9B,IAAI5F,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,IAAIC,KAAK,CAACH,GAAG,CAAC,EAAE,OAAO,GAAG;UAC/D,OAAOA,GAAG,GAAG,GAAG;QAClB,CAAC;QACD6F,KAAK,EAAE;UACLC,QAAQ,EAAE,MAAM;UAChBlB,MAAM,EAAE,CAAC,MAAM,CAAC;UAChBmB,UAAU,EAAE;QACd,CAAC;QACDR,UAAU,EAAE;UAAEE,OAAO,EAAE;QAAM,CAAC;QAC9BO,OAAO,EAAE,CAAC,CAAE;MACd,CAAC;MACDC,MAAM,EAAE;QACNC,KAAK,EAAE,QAAQ;QACfC,KAAK,EAAE;MACT,CAAC;MACDC,KAAK,EAAE;QACLxF,UAAU,EAAE0D,gBAAgB;QAC5B9C,MAAM,EAAE;UACNqE,KAAK,EAAE;YACLjB,MAAM,EAAE,MAAM;YACdkB,QAAQ,EAAE;UACZ,CAAC;UACDE,OAAO,EAAE,CAAC,CAAC;QACb,CAAC;QACDK,UAAU,EAAE;UAAEf,IAAI,EAAE;QAAM,CAAC;QAC3BgB,SAAS,EAAE;UAAEhB,IAAI,EAAE;QAAM;MAC3B,CAAC;MAEDiB,KAAK,EAAE;QAAEjB,IAAI,EAAE;MAAM,CAAC;MACtBV,MAAM,EAAEA,MAAM,CAACC,MAAM;MACrB2B,OAAO,EAAE;QACPC,IAAI,EAAE,CAAC;QACPC,YAAY,EAAE,MAAM;QACpBC,WAAW,EAAE,CAAC;QACdC,KAAK,EAAE;UAAEH,IAAI,EAAE;QAAE;MACnB,CAAC;MACDI,MAAM,EAAE;QACNC,QAAQ,EAAE,QAAQ;QAClBC,eAAe,EAAE,QAAQ;QACzBf,OAAO,EAAE,EAAE;QAAE;QACbQ,OAAO,EAAE;UAAEL,KAAK,EAAE,CAAC;UAAEf,MAAM,EAAE,CAAC;UAAE4B,MAAM,EAAE;QAAE,CAAC;QAC3CxF,MAAM,EAAE;UACNoD,MAAM,EAAE,CAAC,MAAM,CAAC;UAChBqC,eAAe,EAAE,KAAK;UACtBnB,QAAQ,EAAE;QACZ,CAAC;QACDoB,UAAU,EAAE;UAAEC,UAAU,EAAE,EAAE;UAAEC,QAAQ,EAAE;QAAE;MAC5C,CAAC;MACDC,OAAO,EAAE;QACPC,CAAC,EAAE;UACD3B,SAAS,EAAE,SAAAA,CAAU3F,GAAG,EAAE;YACxB,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,IAAIC,KAAK,CAACH,GAAG,CAAC,EAAE,OAAO,GAAG;YAC/D,OAAOA,GAAG,GAAG,GAAG;UAClB;QACF;MACF,CAAC;MACDuH,IAAI,EAAE;QACJjC,IAAI,EAAE,KAAK;QACXkC,OAAO,EAAE;UAAEC,IAAI,EAAE,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAEC,GAAG,EAAE,EAAE;UAAEC,MAAM,EAAE;QAAE;MACrD,CAAC;MACDC,WAAW,EAAE;QACXtB,KAAK,EAAE,CACL;UACEe,CAAC,EAAE,CAAC;UACJQ,WAAW,EAAE,MAAM;UACnBC,WAAW,EAAE,CAAC;UACdC,eAAe,EAAE,CAAC;UAClBC,OAAO,EAAE;QACX,CAAC;MAEL,CAAC;MACD;MACAvC,UAAU,EAAE;QACVD,OAAO,EAAE,IAAI;QACbE,SAAS,EAAE,SAAAA,CAAU3F,GAAG,EAAE4F,IAAI,EAAE;UAC9B,IAAI5F,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,IAAIC,KAAK,CAACH,GAAG,CAAC,EAAE,OAAO,GAAG;UAC/D,MAAMkI,OAAO,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;UAC3B,IAAI,CAAClC,OAAO,GAAGkC,OAAO,CAACtC,IAAI,CAACuC,WAAW,CAAC;UACxC,OAAOnI,GAAG,GAAG,GAAG;QAClB,CAAC;QACD6F,KAAK,EAAE;UACLC,QAAQ,EAAE,MAAM;UAChBlB,MAAM,EAAE,CAAC,MAAM,CAAC;UAChBmB,UAAU,EAAE;QACd,CAAC;QACDR,UAAU,EAAE;UAAEE,OAAO,EAAE;QAAM;MAC/B;IACF,CAAC;;IAGD;IACA,MAAM2C,kBAAkB,GAAG;MACzBlG,MAAM,EAAEqC,OAAO;MACfW,KAAK,EAAE;QACLC,IAAI,EAAE,KAAK;QACXC,MAAM,EAAE,GAAG;QACXC,OAAO,EAAE;UAAEC,IAAI,EAAE;QAAM,CAAC;QACxBE,IAAI,EAAE;UACJC,OAAO,EAAE;QACX;MACF,CAAC;MACDjE,MAAM,EAAEgD,SAAS;MACjBI,MAAM,EAAEA,MAAM,CAACE,WAAW;MAC1BY,UAAU,EAAE;QACVD,OAAO,EAAE,IAAI;QACbE,SAAS,EAAE,SAAAA,CAAU3F,GAAG,EAAE4F,IAAI,EAAE;UAC9B,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAACyC,CAAC,IAAI,CAACzC,IAAI,CAACyC,CAAC,CAACC,OAAO,IAAI,CAAC1C,IAAI,CAACyC,CAAC,CAACC,OAAO,CAACpG,MAAM,EAC/D,OAAO,EAAE;UACX,MAAMnB,KAAK,GAAG6E,IAAI,CAACyC,CAAC,CAACC,OAAO,CAACpG,MAAM,CAAC0D,IAAI,CAACuC,WAAW,CAAC;UACrD,OAAOpH,KAAK,CAACP,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;QACjC,CAAC;QACDqF,KAAK,EAAE;UACLC,QAAQ,EAAE,MAAM;UAChBlB,MAAM,EAAE,CAAC,MAAM,CAAC;UAChBmB,UAAU,EAAE;QACd,CAAC;QACDwC,UAAU,EAAE;UACV9C,OAAO,EAAE;QACX;MACF,CAAC;MACDoB,MAAM,EAAE;QACNC,QAAQ,EAAE,OAAO;QACjBhB,QAAQ,EAAE,MAAM;QAChBC,UAAU,EAAE,KAAK;QACjBS,OAAO,EAAE;UACPL,KAAK,EAAE,EAAE;UACTf,MAAM,EAAE,EAAE;UACV4B,MAAM,EAAE;QACV,CAAC;QACDxF,MAAM,EAAE;UACNoD,MAAM,EAAE,MAAM;UACdqC,eAAe,EAAE,KAAK;UACtBuB,UAAU,EAAE;QACd,CAAC;QACDtB,UAAU,EAAE;UACVC,UAAU,EAAE,CAAC;UACbC,QAAQ,EAAE;QACZ,CAAC;QACDqB,OAAO,EAAE;MACX,CAAC;MACDC,WAAW,EAAE;QACXC,GAAG,EAAE;UACHjD,UAAU,EAAE;YACVkD,MAAM,EAAE;UACV;QACF;MACF,CAAC;MACDvB,OAAO,EAAE;QACPC,CAAC,EAAE;UACD3B,SAAS,EAAE,SAAAA,CAAU3F,GAAG,EAAE;YACxB,OAAO,GAAG,GAAGA,GAAG,CAACQ,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;UACrC;QACF;MACF,CAAC;MACDyF,MAAM,EAAE;QACNX,IAAI,EAAE;MACR,CAAC;MACDuD,UAAU,EAAE,CACV;QACEC,UAAU,EAAE,GAAG;QACf7I,OAAO,EAAE;UACP4G,MAAM,EAAE;YAAEC,QAAQ,EAAE;UAAS;QAC/B;MACF,CAAC;IAEL,CAAC;;IAED;IACA,MAAMiC,sBAAsB,GAAG;MAC7B7G,MAAM,EAAEuC,qBAAqB;MAC7BS,KAAK,EAAE;QACLC,IAAI,EAAE,KAAK;QACXC,MAAM,EAAE,GAAG;QACX4D,OAAO,EAAE,IAAI;QACb3D,OAAO,EAAE;UAAEC,IAAI,EAAE;QAAM,CAAC;QACxBC,UAAU,EAAE;MACd,CAAC;MACDmD,WAAW,EAAE;QACXO,GAAG,EAAE;UACH9B,UAAU,EAAE,KAAK;UACjB+B,WAAW,EAAE,KAAK;UAClBxD,UAAU,EAAE;YACVyD,KAAK,EAAE;cACL1D,OAAO,EAAE,IAAI;cACbO,OAAO,EAAE,CAAC,EAAE;cACZH,KAAK,EAAE;gBACLC,QAAQ,EAAE,MAAM;gBAChBC,UAAU,EAAE,KAAK;gBACjBqD,KAAK,EAAE;cACT,CAAC;cACDzD,SAAS,EAAE,SAAAA,CAAU3F,GAAG,EAAE;gBACxB,OAAOD,cAAc,CAACC,GAAG,CAAC;cAC5B;YACF;UACF;QACF;MACF,CAAC;MACD0F,UAAU,EAAE;QACVD,OAAO,EAAE,KAAK;QACdE,SAAS,EAAE,SAAAA,CAAU3F,GAAG,EAAE;UACxB,OAAOD,cAAc,CAACC,GAAG,CAAC;QAC5B,CAAC;QACD6F,KAAK,EAAE;UACLC,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE,KAAK;UACjBnB,MAAM,EAAE,CAAC,MAAM;QACjB,CAAC;QACDoB,OAAO,EAAE,CAAC,CAAC;QACXT,UAAU,EAAE;UACVE,OAAO,EAAE;QACX,CAAC;QACD8C,UAAU,EAAE;UACV9C,OAAO,EAAE;QACX;MACF,CAAC;MACDW,KAAK,EAAE;QACLxF,UAAU,EAAE8D,iBAAiB;QAC7BlD,MAAM,EAAE;UACNqE,KAAK,EAAE;YACLjB,MAAM,EAAE,MAAM;YACdkB,QAAQ,EAAE;UACZ;QACF,CAAC;QACDO,UAAU,EAAE;UACVf,IAAI,EAAE;QACR,CAAC;QACDgB,SAAS,EAAE;UACThB,IAAI,EAAE;QACR;MACF,CAAC;MACDiB,KAAK,EAAE;QACLjB,IAAI,EAAE;MACR,CAAC;MACDV,MAAM,EAAEA,MAAM,CAACG,eAAe;MAC9B8B,MAAM,EAAE;QACNC,QAAQ,EAAE,QAAQ;QAClBhB,QAAQ,EAAE,MAAM;QAChBC,UAAU,EAAE,KAAK;QACjBS,OAAO,EAAE;UACPL,KAAK,EAAE,CAAC;UACRf,MAAM,EAAE,CAAC;UACT4B,MAAM,EAAE;QACV,CAAC;QACDxF,MAAM,EAAE;UACNoD,MAAM,EAAE,MAAM;UACdqC,eAAe,EAAE;QACnB,CAAC;QACDC,UAAU,EAAE;UACVC,UAAU,EAAE,CAAC;UACbC,QAAQ,EAAE;QACZ,CAAC;QACDpB,OAAO,EAAE,EAAE;QACXqD,WAAW,EAAE;UACXC,gBAAgB,EAAE;QACpB,CAAC;QACDC,WAAW,EAAE;UACXC,mBAAmB,EAAE;QACvB;MACF,CAAC;MACDnC,OAAO,EAAE;QACPC,CAAC,EAAE;UACD3B,SAAS,EAAE,SAAAA,CAAU3F,GAAG,EAAE;YACxB,OAAOD,cAAc,CAACC,GAAG,EAAE;cAAEI,QAAQ,EAAE;YAAK,CAAC,CAAC;UAChD;QACF;MACF,CAAC;MACDmH,IAAI,EAAE;QACJjC,IAAI,EAAE,KAAK;QACXkC,OAAO,EAAE;UACPC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAE,EAAE;UACTC,GAAG,EAAE,EAAE;UACPC,MAAM,EAAE;QACV;MACF,CAAC;MACD3B,MAAM,EAAE;QACNX,IAAI,EAAE;MACR;IACF,CAAC;;IAED;IACA,MAAMmE,mBAAmB,GAAG;MAC1BvH,MAAM,EAAE,CACN;QACEI,IAAI,EAAE,QAAQ;QACd6C,IAAI,EAAE,MAAM;QACZnH,IAAI,EAAE8F;MACR,CAAC,EACD;QACExB,IAAI,EAAE,gBAAgB;QACtB6C,IAAI,EAAE,QAAQ;QACdnH,IAAI,EAAE+F;MACR,CAAC,EACD;QACEzB,IAAI,EAAE,kBAAkB;QACxB6C,IAAI,EAAE,QAAQ;QACdnH,IAAI,EAAEiG;MACR,CAAC,CACF;MACDiB,KAAK,EAAE;QACLE,MAAM,EAAE,GAAG;QACXD,IAAI,EAAE,MAAM;QACZ6D,OAAO,EAAE,IAAI;QACb3D,OAAO,EAAE;UAAEC,IAAI,EAAE;QAAM,CAAC;QACxBC,UAAU,EAAE,aAAa;QACzBC,IAAI,EAAE;UACJC,OAAO,EAAE;QACX;MACF,CAAC;MACDC,UAAU,EAAE;QACVD,OAAO,EAAE,IAAI;QACbiE,eAAe,EAAE,CAAC,CAAC,CAAC;QAAE;QACtB/D,SAAS,EAAE,SAAAA,CAAU3F,GAAG,EAAE;UACxB,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,IAAIC,KAAK,CAACH,GAAG,CAAC,EAAE,OAAO,EAAE;UAC9D,OAAO,GAAG,GAAGqE,uBAAuB,CAACrE,GAAG,CAAC;QAC3C,CAAC;QACD6F,KAAK,EAAE;UACLC,QAAQ,EAAE,MAAM;UAChBlB,MAAM,EAAE,CAAC,SAAS,CAAC;UAAE;UACrBmB,UAAU,EAAE;QACd,CAAC;QACDC,OAAO,EAAE,CAAC,EAAE;QACZT,UAAU,EAAE;UACVE,OAAO,EAAE;QACX,CAAC;QACD8C,UAAU,EAAE;UACV9C,OAAO,EAAE;QACX;MACF,CAAC;MACDQ,MAAM,EAAE;QACNE,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAChBD,KAAK,EAAE;MACT,CAAC;MACDwC,WAAW,EAAE;QACXO,GAAG,EAAE;UACHC,WAAW,EAAE,KAAK;UAClB9D,MAAM,EAAE,IAAI;UACZM,UAAU,EAAE;YACVyD,KAAK,EAAE;cACL1D,OAAO,EAAE,IAAI;cAAE;cACfO,OAAO,EAAE,CAAC,EAAE;cACZH,KAAK,EAAE;gBACLC,QAAQ,EAAE,MAAM;gBAChBC,UAAU,EAAE,KAAK;gBACjBqD,KAAK,EAAE;cACT,CAAC;cACDzD,SAAS,EAAE,SAAAA,CAAU3F,GAAG,EAAE;gBACxB,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,IAAIC,KAAK,CAACH,GAAG,CAAC,EAAE,OAAO,IAAI;gBAChE,OAAO,GAAG,GAAGqE,uBAAuB,CAACrE,GAAG,CAAC;cAC3C;YACF;UACF;QACF;MACF,CAAC;MACD2J,IAAI,EAAE;QACJ1B,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MACnB,CAAC;MACDzG,MAAM,EAAEmD,eAAe;MACvB6B,OAAO,EAAE;QACPC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACfX,QAAQ,EAAE,MAAM;QAChBY,YAAY,EAAE,MAAM;QACpBC,WAAW,EAAE,CAAC;QACdiD,WAAW,EAAE,CAAC;QACdhD,KAAK,EAAE;UACLH,IAAI,EAAE;QACR;MACF,CAAC;MACDL,KAAK,EAAE;QACL5E,MAAM,EAAE;UACNqE,KAAK,EAAE;YACLjB,MAAM,EAAE,MAAM;YACdkB,QAAQ,EAAE;UACZ;QACF,CAAC;QACDO,UAAU,EAAE;UACVf,IAAI,EAAE;QACR,CAAC;QACDgB,SAAS,EAAE;UACThB,IAAI,EAAE;QACR;MACF,CAAC;MACDiB,KAAK,EAAE;QACLjB,IAAI,EAAE;MACR,CAAC;MACDV,MAAM,EAAEA,MAAM,CAACI,YAAY;MAC3B6B,MAAM,EAAE;QACNC,QAAQ,EAAE,QAAQ;QAClBC,eAAe,EAAE,QAAQ;QACzBjB,QAAQ,EAAE,MAAM;QAChBC,UAAU,EAAE,KAAK;QACjBS,OAAO,EAAE;UACPL,KAAK,EAAE,CAAC;UACRf,MAAM,EAAE,CAAC;UACT4B,MAAM,EAAE;QACV,CAAC;QACDxF,MAAM,EAAE;UACNoD,MAAM,EAAE,MAAM;UACdqC,eAAe,EAAE;QACnB,CAAC;QACDC,UAAU,EAAE;UACVC,UAAU,EAAE,EAAE;UACdC,QAAQ,EAAE;QACZ,CAAC;QACDpB,OAAO,EAAE,EAAE;QACXqD,WAAW,EAAE;UACXC,gBAAgB,EAAE;QACpB,CAAC;QACDC,WAAW,EAAE;UACXC,mBAAmB,EAAE;QACvB;MACF,CAAC;MACDnC,OAAO,EAAE;QACPwC,MAAM,EAAE,IAAI;QACZC,SAAS,EAAE,KAAK;QAChBxC,CAAC,EAAE,CACD;UACE3B,SAAS,EAAE,SAAAA,CAAU3F,GAAG,EAAE;YACxB,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,IAAIC,KAAK,CAACH,GAAG,CAAC,EAAE,OAAO,IAAI;YAChE,OAAO,GAAG,GAAGqE,uBAAuB,CAACrE,GAAG,CAAC;UAC3C;QACF,CAAC,EACD;UACE2F,SAAS,EAAE,SAAAA,CAAU3F,GAAG,EAAE;YACxB,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,IAAIC,KAAK,CAACH,GAAG,CAAC,EAAE,OAAO,IAAI;YAChE,OAAO,GAAG,GAAGqE,uBAAuB,CAACrE,GAAG,CAAC;UAC3C;QACF,CAAC,EACD;UACE2F,SAAS,EAAE,SAAAA,CAAU3F,GAAG,EAAE;YACxB,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,IAAIC,KAAK,CAACH,GAAG,CAAC,EAAE,OAAO,IAAI;YAChE,OAAO,GAAG,GAAGqE,uBAAuB,CAACrE,GAAG,CAAC;UAC3C;QACF,CAAC;MAEL,CAAC;MACDuH,IAAI,EAAE;QACJjC,IAAI,EAAE,KAAK;QACXkC,OAAO,EAAE;UACPC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAE,EAAE;UACTC,GAAG,EAAE,EAAE;UACPC,MAAM,EAAE;QACV;MACF;IACF,CAAC;;IAGD;IACA,MAAMmC,mBAAmB,GAAGA,CAACC,GAAG,EAAE/J,OAAO,EAAEgK,SAAS,EAAEC,gBAAgB,GAAG,IAAI,KAAK;MAChF,IAAIF,GAAG,CAACG,OAAO,EAAE;QACf;QACA,IAAID,gBAAgB,IAAIA,gBAAgB,CAACC,OAAO,EAAE;UAChDD,gBAAgB,CAACC,OAAO,CAACC,OAAO,CAAC,CAAC;UAClCF,gBAAgB,CAACC,OAAO,GAAG,IAAI;QACjC;QACAH,GAAG,CAACG,OAAO,CAACE,SAAS,GAAG,EAAE;;QAE1B;QACAC,UAAU,CAAC,MAAM;UACf,IAAIN,GAAG,CAACG,OAAO,EAAE;YACf,IAAI;cACF,MAAMjF,KAAK,GAAG,IAAIrI,UAAU,CAACmN,GAAG,CAACG,OAAO,EAAElK,OAAO,CAAC;cAClDiF,KAAK,CAACqF,MAAM,CAAC,CAAC;;cAEd;cACA,IAAIL,gBAAgB,EAAE;gBACpBA,gBAAgB,CAACC,OAAO,GAAGjF,KAAK;cAClC;;cAEA;cACA,IAAI+E,SAAS,KAAK,SAAS,EAAE;gBAC3BO,MAAM,CAACC,WAAW,GAAGvF,KAAK;cAC5B,CAAC,MAAM,IAAI+E,SAAS,KAAK,cAAc,EAAE;gBACvCO,MAAM,CAACE,gBAAgB,GAAGxF,KAAK;cACjC,CAAC,MAAM,IAAI+E,SAAS,KAAK,kBAAkB,EAAE;gBAC3CO,MAAM,CAACG,oBAAoB,GAAGzF,KAAK;cACrC,CAAC,MAAM,IAAI+E,SAAS,KAAK,kBAAkB,EAAE;gBAC3CO,MAAM,CAACI,iBAAiB,GAAG1F,KAAK;cAClC;YACF,CAAC,CAAC,OAAO2F,KAAK,EAAE;cACd3J,OAAO,CAAC2J,KAAK,CACX,oCAAoCZ,SAAS,SAAS,EACtDY,KACF,CAAC;YACH;UACF;QACF,CAAC,EAAE,EAAE,CAAC;MACR;IACF,CAAC;;IAED;IACA,MAAMC,aAAa,GAAGC,gBAAgB,CAAC,CAAC;;IAExC;IACAD,aAAa,CAACrI,OAAO,CAACyC,KAAK,IAAI;MAC7B,QAAQA,KAAK,CAAC8F,GAAG;QACf,KAAK,WAAW;UACd9F,KAAK,CAACjF,OAAO,GAAGgF,aAAa;UAC7BC,KAAK,CAAC5C,IAAI,GAAG,SAAS;UACtB4C,KAAK,CAACgF,gBAAgB,GAAGvM,cAAc;UACvC;QACF,KAAK,qBAAqB;UACxBuH,KAAK,CAACjF,OAAO,GAAGmI,kBAAkB;UAClClD,KAAK,CAAC5C,IAAI,GAAG,cAAc;UAC3B4C,KAAK,CAACgF,gBAAgB,GAAGtM,mBAAmB;UAC5C;QACF,KAAK,4BAA4B;UAC/BsH,KAAK,CAACjF,OAAO,GAAG8I,sBAAsB;UACtC7D,KAAK,CAAC5C,IAAI,GAAG,kBAAkB;UAC/B4C,KAAK,CAACgF,gBAAgB,GAAGrM,uBAAuB;UAChD;QACF,KAAK,+BAA+B;UAClCqH,KAAK,CAACjF,OAAO,GAAGwJ,mBAAmB;UACnCvE,KAAK,CAAC5C,IAAI,GAAG,kBAAkB;UAC/B4C,KAAK,CAACgF,gBAAgB,GAAGpM,oBAAoB;UAC7C;MACJ;IACF,CAAC,CAAC;;IAEF;IACA,MAAMmN,iBAAiB,GAAIC,SAAS,IAAK;MACvC,OAAOA,SAAS,IAAIA,SAAS,CAACjM,MAAM,GAAG,CAAC,IAAIiM,SAAS,CAACC,IAAI,CAACnL,GAAG,IAAIgB,UAAU,CAAChB,GAAG,CAAC,KAAK,CAAC,CAAC;IAC1F,CAAC;;IAED;IACA8K,aAAa,CAACrI,OAAO,CAAC,CAAC;MAAEuH,GAAG;MAAE/J,OAAO;MAAEqC,IAAI;MAAE4H,gBAAgB;MAAEc;IAAI,CAAC,KAAK;MACvE,IAAIhB,GAAG,CAACG,OAAO,EAAE;QACf,IAAIiB,OAAO,GAAG,KAAK;;QAEnB;QACA,IAAIJ,GAAG,KAAK,WAAW,EAAE;UAAA,IAAAK,eAAA,EAAAC,eAAA;UACvB,MAAM5K,OAAO,GAAG,CAAAtD,UAAU,aAAVA,UAAU,wBAAAiO,eAAA,GAAVjO,UAAU,CAAE6D,GAAG,cAAAoK,eAAA,uBAAfA,eAAA,CAAiBxK,GAAG,CAACC,IAAI,IAAIE,UAAU,CAACF,IAAI,CAACG,GAAG,IAAI,CAAC,CAAC,CAAC,KAAI,EAAE;UAC7E,MAAMN,OAAO,GAAG,CAAAvD,UAAU,aAAVA,UAAU,wBAAAkO,eAAA,GAAVlO,UAAU,CAAEgE,GAAG,cAAAkK,eAAA,uBAAfA,eAAA,CAAiBzK,GAAG,CAACC,IAAI,IAAIE,UAAU,CAACF,IAAI,CAACM,GAAG,IAAI,CAAC,CAAC,CAAC,KAAI,EAAE;UAC7EgK,OAAO,GAAGH,iBAAiB,CAACvK,OAAO,CAAC,IAAIuK,iBAAiB,CAACtK,OAAO,CAAC;QACpE,CAAC,MAAM,IAAIqK,GAAG,KAAK,qBAAqB,EAAE;UACxC,MAAM;YAAEhN,IAAI,EAAEuG;UAAQ,CAAC,GAAGhD,sBAAsB,CAAC,CAAC;UAClD6J,OAAO,GAAG7G,OAAO,IAAIA,OAAO,CAACtF,MAAM,GAAG,CAAC,IAAIsF,OAAO,CAAC4G,IAAI,CAACnL,GAAG,IAAIgB,UAAU,CAAChB,GAAG,CAAC,KAAK,CAAC,CAAC;QACvF,CAAC,MAAM,IAAIgL,GAAG,KAAK,4BAA4B,EAAE;UAC/C,MAAM;YAAE9I,MAAM,EAAEuC;UAAsB,CAAC,GAAGzC,0BAA0B,CAAC,CAAC;UACtEoJ,OAAO,GAAG3G,qBAAqB,IAAIA,qBAAqB,CAACxF,MAAM,GAAG,CAAC,IAC1DwF,qBAAqB,CAAC0G,IAAI,CAACjJ,MAAM,IAAIA,MAAM,CAAClE,IAAI,IAAIkE,MAAM,CAAClE,IAAI,CAACmN,IAAI,CAACnL,GAAG,IAAIgB,UAAU,CAAChB,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;QAC9G,CAAC,MAAM,IAAIgL,GAAG,KAAK,+BAA+B,EAAE;UAClD,MAAM;YAAElH,MAAM;YAAEC,UAAU;YAAEE;UAAc,CAAC,GAAGN,uBAAuB,CAAC,CAAC;UACvEyH,OAAO,GAAGH,iBAAiB,CAACnH,MAAM,CAAC,IAAImH,iBAAiB,CAAClH,UAAU,CAAC,IAAIkH,iBAAiB,CAAChH,aAAa,CAAC;QAC1G;QAEA,IAAImH,OAAO,EAAE;UACXrB,mBAAmB,CAACC,GAAG,EAAE/J,OAAO,EAAEqC,IAAI,EAAE4H,gBAAgB,CAAC;QAC3D,CAAC,MACI;UACHF,GAAG,CAACG,OAAO,CAACE,SAAS,GAAG,kFAAkF/H,IAAI,CAAChE,WAAW,CAAC,CAAC,uBAAuB;QACrJ;MACF;IACF,CAAC,CAAC;;IAEF;IACA,CAACf,SAAS,EAAEC,cAAc,EAAEC,kBAAkB,EAAEC,eAAe,CAAC,CAAC+E,OAAO,CAAEuH,GAAG,IAAK;MAChF,IAAIA,GAAG,CAACG,OAAO,IAAI,CAACW,aAAa,CAACK,IAAI,CAACjG,KAAK,IAAIA,KAAK,CAAC8E,GAAG,KAAKA,GAAG,CAAC,EAAE;QAClEA,GAAG,CAACG,OAAO,CAACE,SAAS,GAAG,EAAE;MAC5B;IACF,CAAC,CAAC;EACJ,CAAC;;EAGD;EACA,MAAMkB,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAM;MAAE7K,OAAO;MAAEC;IAAQ,CAAC,GAAGF,iBAAiB,CAAC,CAAC;IAChD,MAAM;MAAEzC,IAAI,EAAEuG;IAAQ,CAAC,GAAGhD,sBAAsB,CAAC,CAAC;IAClD,MAAM;MAAEW,MAAM,EAAEuC;IAAsB,CAAC,GAAGzC,0BAA0B,CAAC,CAAC;IACtE,MAAM;MAAE8B;IAAO,CAAC,GAAGH,uBAAuB,CAAC,CAAC;IAE5C,OACEjD,OAAO,CAACzB,MAAM,GAAG,CAAC,IAClB0B,OAAO,CAAC1B,MAAM,GAAG,CAAC,IAClBsF,OAAO,CAACtF,MAAM,GAAG,CAAC,IAClBwF,qBAAqB,CAACxF,MAAM,GAAG,CAAC,IAChC6E,MAAM,CAAC7E,MAAM,GAAG,CAAC;EAErB,CAAC;EAED,MAAMuM,kBAAkB,GAAGA,CAACC,SAAS,EAAEC,UAAU,KAAK;IACpD,MAAM9L,UAAU,GAAG,CACjB,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EACtD,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CACjE;IAED,IAAI,CAAC6L,SAAS,IAAI,CAACC,UAAU,EAAE;MAC7B,OAAO,GAAG,CAAC,CAAC;IACd;IAEA,MAAMC,cAAc,GAAG/L,UAAU,CAAC8L,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;;IAEnD,OAAO,GAAGC,cAAc,IAAIF,SAAS,EAAE;EACzC,CAAC;EAED,MAAMG,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAM/F,KAAK,GAAG;MAAE,GAAG5I;IAAgB,CAAC;IACpC,IAAI4I,KAAK,CAACC,QAAQ,EAAE;MAClB,MAAMA,QAAQ,GAAG5C,QAAQ,CAAC2C,KAAK,CAACC,QAAQ,CAAC;MACzCD,KAAK,CAACC,QAAQ,GAAG,GAAGA,QAAQ,GAAG,CAAC,IAAI;IACtC;IACA,OAAOD,KAAK;EACd,CAAC;EAED,MAAMgG,iBAAiB,GAAIC,WAAW,IAAK;IACzC,IAAI,CAACA,WAAW,EAAE,OAAO,EAAE;IAE3B,IAAIA,WAAW,CAAC7M,MAAM,GAAG,EAAE,EAAE;MAC3B,OAAO6M,WAAW,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;IAC7C;IAEA,OAAOD,WAAW;EACpB,CAAC;;EAED;EACA,MAAMf,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMiB,SAAS,GAAG,CAChB;MACEhB,GAAG,EAAE,WAAW;MAChBiB,KAAK,EAAE,6BAA6B;MACpCjC,GAAG,EAAEzM,SAAS;MACd0C,OAAO,EAAE,IAAI;MAAE;MACfmL,OAAO,EAAEA,CAAA,KAAM;QACb,MAAM;UAAE1K,OAAO;UAAEC;QAAQ,CAAC,GAAGF,iBAAiB,CAAC,CAAC;QAChD,OAAOC,OAAO,CAACzB,MAAM,GAAG,CAAC,IAAI0B,OAAO,CAAC1B,MAAM,GAAG,CAAC;MACjD,CAAC;MACDiN,cAAc,EAAE,IAAI;MACpBC,WAAW,EAAE;QACXC,QAAQ,EAAE,CACR;UACEH,KAAK,EAAE,kBAAkB;UACzBI,OAAO,EAAE,sBAAsBjP,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE0O,WAAW;QACxD,CAAC,EACD;UACEG,KAAK,EAAE,kBAAkB;UACzBI,OAAO,EAAE;QACX,CAAC;MAEL;IACF,CAAC,EACD;MACErB,GAAG,EAAE,qBAAqB;MAC1BiB,KAAK,EAAE,wBAAwB;MAC/BjC,GAAG,EAAExM,cAAc;MACnByC,OAAO,EAAE,IAAI;MACbmL,OAAO,EAAEA,CAAA,KAAM;QACb,MAAM;UAAEpN,IAAI,EAAEuG;QAAQ,CAAC,GAAGhD,sBAAsB,CAAC,CAAC;QAClD,OAAOgD,OAAO,CAACtF,MAAM,GAAG,CAAC;MAC3B,CAAC;MACDqN,iBAAiB,EAAE;IACrB,CAAC,EACD;MACEtB,GAAG,EAAE,4BAA4B;MACjCiB,KAAK,EAAE,gCAAgC;MACvCjC,GAAG,EAAEvM,kBAAkB;MACvBwC,OAAO,EAAE,IAAI;MACbmL,OAAO,EAAEA,CAAA,KAAM;QACb,MAAM;UAAElJ,MAAM,EAAEuC;QAAsB,CAAC,GAAGzC,0BAA0B,CAAC,CAAC;QACtE,OAAOyC,qBAAqB,CAACxF,MAAM,GAAG,CAAC;MACzC,CAAC;MACDsN,cAAc,EAAE;IAClB,CAAC,EACD;MACEvB,GAAG,EAAE,+BAA+B;MACpCiB,KAAK,EAAE,oCAAoC;MAC3CjC,GAAG,EAAEtM,eAAe;MACpBuC,OAAO,EAAE,IAAI;MACbmL,OAAO,EAAEA,CAAA,KAAM;QACb,MAAM;UAAEtH;QAAO,CAAC,GAAGH,uBAAuB,CAAC,CAAC;QAC5C,OAAOG,MAAM,CAAC7E,MAAM,GAAG,CAAC;MAC1B,CAAC;MACDuN,qBAAqB,EAAE;IACzB,CAAC,CACF;;IAED;IACA,OAAOR,SAAS,CAACS,MAAM,CAACvH,KAAK,IAC3BzG,kBAAkB,CAACyG,KAAK,CAAC8F,GAAG,CAAC,IAAI9F,KAAK,CAACkG,OAAO,CAAC,CACjD,CAAC;EACH,CAAC;;EAED;EACA,MAAMN,aAAa,GAAGC,gBAAgB,CAAC,CAAC;;EAExC;EACA;EACA,MAAM2B,cAAc,GAAG5B,aAAa,CAAChL,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAChD,MAAM6M,cAAc,GAAG7B,aAAa,CAAChL,KAAK,CAAC,CAAC,CAAC;;EAE7C;EACA,MAAM8M,WAAW,GAAI1H,KAAK,iBACxBnI,OAAA;IAAqB8P,SAAS,EAAE,2CAA2C3H,KAAK,CAACqH,cAAc,IAAI,EAAE,EAAG;IAAAO,QAAA,gBACtG/P,OAAA;MACE8P,SAAS,EAAE,6CAA6C3H,KAAK,CAACoH,iBAAiB,IAAI,EAAE,EAAG;MACxFzG,KAAK,EAAE3I,mBAAoB;MAAA4P,QAAA,EAE1B5H,KAAK,CAAC+G;IAAK;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eACNnQ,OAAA;MAAKiN,GAAG,EAAE9E,KAAK,CAAC8E,GAAI;MAAC6C,SAAS,EAAE3H,KAAK,CAACsH,qBAAqB,IAAI;IAAG;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EACxEhI,KAAK,CAACgH,cAAc,iBACnBnP,OAAA;MAAK8P,SAAS,EAAC,MAAM;MAAAC,QAAA,EAClB5H,KAAK,CAACiH,WAAW,CAACC,QAAQ,CAACvL,GAAG,CAAC,CAACsM,OAAO,EAAEC,KAAK,kBAC7CrQ,OAAA;QAAiB8P,SAAS,EAAEO,KAAK,KAAKlI,KAAK,CAACiH,WAAW,CAACC,QAAQ,CAACnN,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,MAAO;QAAA6N,QAAA,gBAC5F/P,OAAA;UACE8P,SAAS,EAAC,qCAAqC;UAC/ChH,KAAK,EAAE;YAAE,GAAG3I,mBAAmB;YAAE6I,UAAU,EAAE;UAAU,CAAE;UAAA+G,QAAA,EAExDK,OAAO,CAAClB;QAAK;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACNnQ,OAAA;UACE8P,SAAS,EAAC,+BAA+B;UACzChH,KAAK,EAAE1I,gBAAiB;UAAA2P,QAAA,EAEvBK,OAAO,CAACd;QAAO;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC;MAAA,GAZIE,KAAK;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAaV,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA,GA3BOhI,KAAK,CAAC8F,GAAG;IAAA+B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OA4Bd,CACN;;EAED;EACA,IAAIpC,aAAa,CAAC7L,MAAM,KAAK,CAAC,EAAE;IAC9B,OAAO,IAAI;EACb;EAEA,oBACElC,OAAA;IAAK8P,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClB/P,OAAA;MAAK8P,SAAS,EAAC,qEAAqE;MAAAC,QAAA,gBAElF/P,OAAA;QAAK8P,SAAS,EAAC,+FAA+F;QAAAC,QAAA,gBAC5G/P,OAAA;UACE8P,SAAS,EAAC,sCAAsC;UAChDhH,KAAK,EAAE5I,eAAgB;UAAA6P,QAAA,EACxB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLnQ,OAAA;UAAG8P,SAAS,EAAC,2BAA2B;UAAChH,KAAK,EAAE+F,iBAAiB,CAAC,CAAE;UAAAkB,QAAA,GACjEtB,kBAAkB,CAACpO,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEiQ,WAAW,EAAEjQ,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEkQ,YAAY,CAAC,EAAC,KAAG,EAACzB,iBAAiB,CAACzO,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE0O,WAAW,CAAC;QAAA;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAGLR,cAAc,CAAC7L,GAAG,CAACqE,KAAK,IAAI0H,WAAW,CAAC1H,KAAK,CAAC,CAAC;IAAA;MAAA6H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAE7C,CAAC,EAGLP,cAAc,CAAC1N,MAAM,GAAG,CAAC,iBACxBlC,OAAA;MAAK8P,SAAS,EAAC,gEAAgE;MAAAC,QAAA,gBAC7E/P,OAAA;QAAK8P,SAAS,EAAC,oGAAoG;QAAAC,QAAA,gBACjH/P,OAAA;UACE8P,SAAS,EAAC,sCAAsC;UAChDhH,KAAK,EAAE5I,eAAgB;UAAA6P,QAAA,EACxB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLnQ,OAAA;UAAG8P,SAAS,EAAC,2BAA2B;UAAChH,KAAK,EAAE+F,iBAAiB,CAAC,CAAE;UAAAkB,QAAA,GACjEtB,kBAAkB,CAACpO,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEiQ,WAAW,EAAEjQ,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEkQ,YAAY,CAAC,EAAC,KAAG,EAACzB,iBAAiB,CAACzO,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE0O,WAAW,CAAC;QAAA;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAGLP,cAAc,CAAC9L,GAAG,CAACqE,KAAK,IAAI0H,WAAW,CAAC1H,KAAK,CAAC,CAAC;IAAA;MAAA6H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC5P,EAAA,CAhqCIN,uBAAuB;AAAAuQ,EAAA,GAAvBvQ,uBAAuB;AAkqC7B,eAAeA,uBAAuB;AAAC,IAAAuQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}